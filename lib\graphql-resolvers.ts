// RESOLVERS GRAPHQL SIMPLIFICADOS
// Implementação inicial dos resolvers principais

import { 
  getAllInfluencers, 
  getInfluencerById, 
  addInfluencer, 
  updateInfluencer, 
  deleteInfluencer 
} from './firebase';
import { calculateTotalFollowers, calculateTotalViews } from '../types/influencer';
import { addFinancial } from './firebase-financials';
import { FinancialCacheService } from './firebase-financial-cache';
import { FinancialDenormalizationService } from './financial-denormalization-service';
import { getCurrentPricing, getPricingHistory, createPricing, updatePricing, deletePricing } from './firebase-pricing';
import { getCurrentDemographics, createDemographic, AudienceDemographic } from './firebase-demographics';
import { statsCache } from './stats-cache-service';
// ⚠️ DESABILITADO: Firebase Auth removido em favor do Clerk
// import { auth } from 'firebase-admin';
import { getApps, initializeApp, cert } from 'firebase-admin/app';
import { db } from './firebase';

// PubSub para subscriptions (simplificado)
const pubsub = {
  publish: (event: string, payload: any) => {
    // Publish event silently
  }
};

// Inicializar Firebase Admin se necessário
if (getApps().length === 0) {
  try {
    const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');
    initializeApp({
      credential: cert(serviceAccount)
    });
  } catch (error) {
   
  }
}

// Interface para context
interface GraphQLContext {
  user: { uid: string; email?: string } | null;
}

// Função auxiliar para buscar dados de pricing
async function getPricingData(influencerId: string, userId: string) {
  try {
    // Implementação simplificada - retorna dados padrão
    return {
      hasFinancialData: false,
      priceRange: "",
      avgPrice: 0,
      lastPriceUpdate: new Date(),
      isNegotiable: false
    };
  } catch (error) {
   
    return null;
  }
}

// ===== RESOLVERS PRINCIPAIS =====

export const resolvers = {
  // ===== QUERIES =====
  Query: {
    // Buscar influenciador por ID
    influencer: async (_: any, { id, userId }: { id: string, userId: string }) => {
      try {
        console.log(`🔒 [SECURITY] Buscando influenciador ${id} para userId: ${userId}`);

        // 🚨 VALIDAÇÃO DE SEGURANÇA: Exigir userId
        if (!userId || userId === 'undefined' || userId === 'null') {
          console.warn('⚠️ [SECURITY] Tentativa de buscar influenciador sem userId');
          return null;
        }

        const influencerDoc = await db.collection('influencers').doc(id).get();

        if (!influencerDoc.exists) {
          return null;
        }

        const data = influencerDoc.data();
        if (!data) {
          return null;
        }

        // 🔒 VALIDAÇÃO: Verificar se o influenciador pertence ao usuário
        if (data.userId !== userId) {
          console.warn(`⚠️ [SECURITY] Tentativa de acesso a influenciador de outro usuário: ${id}`);
          return null;
        }

        console.log(`✅ [SECURITY] Influenciador ${id} acessado com sucesso pelo usuário: ${userId}`);
        return {
          id: influencerDoc.id,
          ...data,
          // 🔥 CORREÇÃO: Garantir valores padrão para campos obrigatórios
          rating: data.rating || 4.0,
          totalFollowers: data.totalFollowers || 0,
          totalViews: data.totalViews || 0,
          engagementRate: data.engagementRate || 0,
          categories: data.categories || [],
          campaigns: data.campaigns || [],
          brands: data.brands || [],
          tags: data.tags || [],
          notes: data.notes || [],
          mainCategoriesData: data.mainCategoriesData || [],
          pricing: await getPricingData(influencerDoc.id, data.userId),
          createdAt: data.createdAt?.toDate?.() || new Date(),
          updatedAt: data.updatedAt?.toDate?.() || new Date()
        };
      } catch (error) {
        console.error('❌ [SECURITY] Erro ao buscar influenciador:', error);
        throw new Error('Erro ao buscar influenciador');
      }
    },

    // Buscar lista de influenciadores com filtros básicos
    influencers: async (_: any, { userId, filters = {}, pagination = {} }: { userId: string, filters?: any, pagination?: any }, context: GraphQLContext) => {
      try {
        let query = db.collection('influencers').where('userId', '==', userId);
        
        // Aplicar filtros
        if (filters.search) {
          // Em produção, usar Algolia ou similar para busca textual
        }
        
        if (filters.category && filters.category !== 'all') {
          query = query.where('category', '==', filters.category);
        }
        
        if (filters.isAvailable !== undefined) {
          query = query.where('isAvailable', '==', filters.isAvailable);
        }
        
        // Ordenação
        query = query.orderBy('createdAt', 'desc');
        
        // Paginação
        const limit = pagination.limit || 20;
        const offset = pagination.offset || 0;
        
        if (offset > 0) {
          query = query.offset(offset);
        }
        
        query = query.limit(limit);
        
        const snapshot = await query.get();
        
        const nodes = await Promise.all(
          snapshot.docs.map(async (doc) => {
            const data = doc.data();
            return {
              id: doc.id,
              ...data,
              // 🔥 CORREÇÃO: Garantir valores padrão para campos obrigatórios
              rating: data.rating || 4.0,
              totalFollowers: data.totalFollowers || 0,
              totalViews: data.totalViews || 0,
              engagementRate: data.engagementRate || 0,
              categories: data.categories || [],
              campaigns: data.campaigns || [],
              brands: data.brands || [],
              tags: data.tags || [],
              notes: data.notes || [],
              mainCategoriesData: data.mainCategoriesData || [],
              pricing: await getPricingData(doc.id, data.userId),
              createdAt: data.createdAt?.toDate?.() || new Date(),
              updatedAt: data.updatedAt?.toDate?.() || new Date()
            };
          })
        );
        
        // Para obter o total real, faríamos uma query separada em produção
        const totalCount = snapshot.size;
        
        return {
          nodes,
          totalCount,
          hasNextPage: snapshot.size === limit,
          hasPreviousPage: offset > 0
        };
      } catch (error) {
       
        throw new Error('Erro ao buscar influenciadores');
      }
    },

    // Buscar por faixa de preço
    influencersByPriceRange: async (parent: any, args: any, context: GraphQLContext) => {
      const { priceRange, userId, limit = 20 } = args;
      
      try {
        return await FinancialDenormalizationService.getInfluencersByPriceRange(
          priceRange.toLowerCase(),
          userId,
          limit
        );
      } catch (error) {
       
        throw new Error('Erro ao buscar influenciadores por preço');
      }
    },

    // ✨ NOVA QUERY OTIMIZADA: Buscar múltiplos influenciadores por IDs
    influencersByIds: async (parent: any, { ids, userId, proposalId }: { ids: string[], userId: string, proposalId?: string }, context: GraphQLContext) => {
      const startTime = Date.now();

      console.log('🚀 [GraphQL] influencersByIds iniciado:', {
        totalIds: ids?.length || 0,
        userId,
        proposalId,
        isSharedList: proposalId === 'shared_list'
      });
      

      const result = {
        influencers: [] as any[],
        foundIds: [] as string[],
        notFoundIds: [] as string[],
        invalidIds: [] as string[],
        totalRequested: ids.length,
        totalFound: 0,
        totalNotFound: 0,
        totalInvalid: 0,
        processingTimeMs: 0,
        hasPartialFailure: false,
        errors: [] as string[]
      };

      try {
        // Validar IDs
        const validIds = ids.filter(id => id && typeof id === 'string' && id.trim().length > 0);
        result.invalidIds = ids.filter(id => !validIds.includes(id));
        result.totalInvalid = result.invalidIds.length;

        if (validIds.length === 0) {
          result.errors.push('Nenhum ID válido fornecido');
          result.processingTimeMs = Date.now() - startTime;
          return result;
        }

        // Verificar acesso: ownership direto OU via proposta OU lista compartilhada
        let hasAccess = false;
        let isSharedList = false;

        if (proposalId === 'shared_list') {
          // 🔗 NOVO: Acesso especial para listas compartilhadas
          hasAccess = true;
          isSharedList = true;
          console.log('🔗 [GraphQL] Acesso permitido para lista compartilhada');
        } else if (proposalId) {
          // Verificar acesso via proposta (para membros convidados)
          try {
            const { ProposalService } = await import('@/services/proposal-service');
            const isOwner = await ProposalService.canUserAccessProposal(proposalId, userId);

            if (isOwner) {
              hasAccess = true;

            } else {
              // Verificar via Clerk metadata
              const { clerkClient } = await import('@clerk/nextjs/server');
              const clerk = await clerkClient();
              const user = await clerk.users.getUser(userId);

              const proposalAccess = (user.unsafeMetadata as any)?.p?.[proposalId];

              if (proposalAccess && ['a', 'e', 'v'].includes(proposalAccess)) {
                hasAccess = true;

              }
            }
          } catch (error) {

          }
        } else {
          // Acesso direto (apenas influencers do usuário)
          hasAccess = true;

        }

        if (!hasAccess) {
          result.errors.push('Acesso negado');
          result.processingTimeMs = Date.now() - startTime;
          return result;
        }

        // Buscar influencers
        const influencersPromises = validIds.map(async (id) => {
          try {
            const doc = await db.collection('influencers').doc(id).get();
            
            if (!doc.exists) {
              result.notFoundIds.push(id);
              return null;
            }

            const data = doc.data();
            if (!data) {
              result.notFoundIds.push(id);
              return null;
            }

            // Verificar ownership: se não há proposalId, deve ser do usuário
            // Se há proposalId, o acesso já foi verificado acima
            // Se é lista compartilhada, permitir acesso independente do ownership
            if (!proposalId && !isSharedList && data.userId !== userId) {
              result.notFoundIds.push(id);
              return null;
            }

            // Se há proposalId ou é lista compartilhada, permitir acesso independente do ownership
            // (o acesso à proposta ou lista compartilhada já foi verificado)

            result.foundIds.push(id);
            
            // Processar dados do influencer
            return {
              id: doc.id,
              ...data, // 🔥 IMPORTANTE: Este spread inclui TODOS os campos do Firestore, incluindo os campos de views específicos
              rating: data.rating || 4.0,
              totalFollowers: data.totalFollowers || 0,
              totalViews: data.totalViews || 0,
              engagementRate: data.engagementRate || 0,
              categories: data.categories || [],
              campaigns: data.campaigns || [],
              brands: data.brands || [],
              tags: data.tags || [],
              notes: data.notes || [],
              mainCategoriesData: data.mainCategoriesData || [],
              // 🎯 CAMPOS DE VIEWS ESPECÍFICOS - garantidos pelo spread ...data acima
              // instagramStoriesViews, instagramReelsViews, tiktokVideoViews, 
              // youtubeShortsViews, youtubeLongFormViews, facebookViews,
              // facebookReelsViews, facebookStoriesViews - todos incluídos
              createdAt: data.createdAt?.toDate?.() || new Date(),
              updatedAt: data.updatedAt?.toDate?.() || new Date()
            };
            
          } catch (error) {
            
            result.notFoundIds.push(id);
            return null;
          }
        });

        const influencersResults = await Promise.all(influencersPromises);
        result.influencers = influencersResults.filter(inf => inf !== null);

        result.totalFound = result.influencers.length;
        result.totalNotFound = result.notFoundIds.length;
        result.hasPartialFailure = result.totalNotFound > 0 || result.totalInvalid > 0;
        result.processingTimeMs = Date.now() - startTime;

       

        return result;

      } catch (error: any) {
        result.errors.push(`Erro interno: ${error?.message || 'Erro desconhecido'}`);
        result.processingTimeMs = Date.now() - startTime;
       
        return result;
      }
    },

    // 🎯 NOVA QUERY: Buscar IDs de influencers de uma proposta
    proposalInfluencers: async (parent: any, { proposalId, userId }: { proposalId: string, userId: string }, context: GraphQLContext) => {
     

      // Garantir que sempre retornamos um objeto válido
      const result = {
        influencerIds: [] as string[],
        totalCount: 0,
        success: false,
        proposalId: proposalId || '',
        errors: [] as string[]
      };

      try {
        // Validar parâmetros obrigatórios
        if (!proposalId || !userId) {
          result.errors.push('proposalId e userId são obrigatórios');
         
          return result;
        }

        // Verificar acesso à proposta (ownership direto + Clerk metadata)
        let hasAccess = false;
        
        // 1. Verificar ownership via ProposalService
        try {
          const { ProposalService } = await import('@/services/proposal-service');
          const isOwner = await ProposalService.canUserAccessProposal(proposalId, userId);
          
          if (isOwner) {
            hasAccess = true;
           
          }
        } catch (serviceError) {
         
        }
        
        if (!hasAccess) {
          // 2. Verificar acesso via Clerk metadata (membro convidado)
          try {
            const { clerkClient } = await import('@clerk/nextjs/server');
            const clerk = await clerkClient();
            const user = await clerk.users.getUser(userId);
            
            const proposalAccess = (user.unsafeMetadata as any)?.p?.[proposalId];
            
            if (proposalAccess && ['a', 'e', 'v'].includes(proposalAccess)) {
              hasAccess = true;
             
            } else {
             
            }
            
          } catch (clerkError) {
           
          }
        }

        if (!hasAccess) {
          result.errors.push('Acesso negado à proposta');
         
          return result;
        }

        // Buscar influencers da subcoleção da proposta
        try {
          const influencersSnapshot = await db
            .collection('proposals')
            .doc(proposalId)
            .collection('influencers')
            .get();

          const influencerIds = influencersSnapshot.docs.map(doc => doc.id);
          
          result.influencerIds = influencerIds;
          result.totalCount = influencerIds.length;
          result.success = true;

          

        } catch (firebaseError: any) {
          result.errors.push(`Erro ao acessar Firebase: ${firebaseError.message}`);
         
        }

        return result;

      } catch (error: any) {
        result.errors.push(`Erro interno: ${error?.message || 'Erro desconhecido'}`);
       
        return result;
      }
    },

    // Dados financeiros de um influenciador
    influencerFinancial: async (parent: any, { influencerId }: { influencerId: string }, context: GraphQLContext) => {
      try {
        return await FinancialCacheService.getFinancialData(influencerId);
      } catch (error) {
       
        throw new Error('Erro ao buscar dados financeiros');
      }
    },

    // Estatísticas financeiras
    financialStats: async (_: any, { userId }: { userId: string }) => {
      try {
        // Buscar contadores do Firestore
        const [influencersSnapshot, brandsSnapshot] = await Promise.all([
          db.collection('influencers').where('userId', '==', userId).get(),
          db.collection('brands').where('userId', '==', userId).get()
        ]);
        
        // Calcular estatísticas agregadas
        let totalFollowers = 0;
        let totalViews = 0;
        
        influencersSnapshot.docs.forEach(doc => {
          const data = doc.data();
          totalFollowers += data.totalFollowers || 0;
          totalViews += data.totalFollowers || 0; // Simplificação: usar followers como proxy para views
        });
        
        const stats = {
          totalInfluencers: influencersSnapshot.size,
          totalViews: totalViews,
          totalFollowers: totalFollowers,
          totalBrands: brandsSnapshot.size,
          totalCampaigns: 0 // Placeholder até implementar campanhas
        };
        
        return stats;
      } catch (error) {
       
        throw new Error('Erro ao buscar estatísticas');
      }
    },

    // Estatísticas do cache
    cacheStats: async () => {
      return {
        hits: 0,
        misses: 0,
        evictions: 0,
        size: 0,
        hitRate: 0
      };
    },

    // Dashboard data
    dashboardData: async (_: any, { userId }: { userId: string }) => {
      try {
        // Implementação simplificada - combinar várias queries
        return {
          totalInfluencers: 0,
          totalWithFinancialData: 0,
          totalCampaigns: 0,
          totalBrands: 0,
          financialStats: {
            totalWithPricing: 0,
            avgPrice: 0,
            minPrice: 0,
            maxPrice: 0
          },
          recentInfluencers: [],
          topPerformers: []
        };
      } catch (error) {
       
        throw new Error('Erro ao buscar dados do dashboard');
      }
    },

    // ===== CATEGORIES =====
    categories: async (_: any, { userId }: { userId?: string }) => {
      const { CategoryService } = await import('@/services/category-service-unified');
      const { withGraphQLCategoryAccess, logCategoryOperation } = await import('@/lib/middleware/category-access');

      // 🔄 CORREÇÃO: Permitir acesso público para categorias gerais
      return withGraphQLCategoryAccess(async (parent, args, context) => {
        try {
          // 🔄 CORREÇÃO: Usar userId do parâmetro se fornecido, senão usar do contexto
          const effectiveUserId = userId || context.userId;

          logCategoryOperation('GET_CATEGORIES', null, effectiveUserId || 'público', true, { includePublic: true });

          // 🔒 SEGURANÇA: Se não há userId (nem no parâmetro nem no contexto), retornar categorias públicas básicas
          if (!effectiveUserId) {
            console.log('🔓 [CATEGORIES] Retornando categorias públicas para contexto não autenticado');
            return [
              { id: 'all', name: 'Todas', slug: 'all', description: 'Todas as categorias', count: null, userId: null, isActive: true, createdAt: new Date(), updatedAt: new Date() },
              { id: 'lifestyle', name: 'Lifestyle', slug: 'lifestyle', description: 'Conteúdo de lifestyle', count: null, userId: null, isActive: true, createdAt: new Date(), updatedAt: new Date() },
              { id: 'fashion', name: 'Moda', slug: 'fashion', description: 'Conteúdo de moda', count: null, userId: null, isActive: true, createdAt: new Date(), updatedAt: new Date() },
              { id: 'beauty', name: 'Beleza', slug: 'beauty', description: 'Conteúdo de beleza', count: null, userId: null, isActive: true, createdAt: new Date(), updatedAt: new Date() },
              { id: 'fitness', name: 'Fitness', slug: 'fitness', description: 'Conteúdo de fitness', count: null, userId: null, isActive: true, createdAt: new Date(), updatedAt: new Date() },
              { id: 'food', name: 'Gastronomia', slug: 'food', description: 'Conteúdo gastronômico', count: null, userId: null, isActive: true, createdAt: new Date(), updatedAt: new Date() }
            ];
          }

          // 🔒 SEGURANÇA: Para usuários autenticados, buscar suas categorias + públicas
          console.log(`🔒 [CATEGORIES] Buscando categorias para usuário autenticado: ${effectiveUserId}`);
          return await CategoryService.getCategories(
            effectiveUserId,
            { includePublic: true, isActive: true },
            { orderBy: 'name', orderDirection: 'asc' }
          );

        } catch (error) {
          const effectiveUserId = userId || context.userId;
          logCategoryOperation('GET_CATEGORIES', null, effectiveUserId || 'público', false, { error: error.message });

          // 🔄 FALLBACK: Em caso de erro, retornar categorias básicas
          console.error('❌ [CATEGORIES] Erro ao buscar categorias, retornando fallback:', error);
          return [
            { id: 'all', name: 'Todas', slug: 'all', description: 'Todas as categorias', count: null, userId: null, isActive: true, createdAt: new Date(), updatedAt: new Date() },
            { id: 'lifestyle', name: 'Lifestyle', slug: 'lifestyle', description: 'Conteúdo de lifestyle', count: null, userId: null, isActive: true, createdAt: new Date(), updatedAt: new Date() }
          ];
        }
      }, true)(_, { userId }, {}); // 🔄 CORREÇÃO: Permitir acesso público
    },

    userCategories: async (_: any, { userId }: { userId: string }) => {
      try {
        
        if (!userId || userId === 'undefined' || userId === 'null') {
         
          return [];
        }

        // Buscar categorias do usuário no Firestore
        const snapshot = await db.collection('categories')
          .where('userId', '==', userId)
          .where('isActive', '==', true)
          .get();

       

        if (snapshot.empty) {
         
          return [];
        }

        const categories = snapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            name: data.name || 'Categoria sem nome',
            slug: data.slug || (data.name ? data.name.toLowerCase().replace(/\s+/g, '-') : null),
            description: data.description || null,
            count: data.count || null,
            userId: data.userId || userId,
            isActive: data.isActive !== false,
            createdAt: data.createdAt?.toDate?.() || new Date(),
            updatedAt: data.updatedAt?.toDate?.() || new Date()
          };
        });

       
        return categories;
      } catch (error) {
       
        return [];
      }
    },

    category: async (_: any, { id, userId }: { id: string, userId: string }) => {
      try {
        if (!userId || userId === 'undefined' || userId === 'null') {
          return null;
        }

        const doc = await db.collection('categories').doc(id).get();
        
        if (!doc.exists) {
          return null;
        }
        
        const data = doc.data();
        if (!data || data.userId !== userId) {
          return null;
        }
        
        return {
          id: doc.id,
          name: data.name || 'Categoria sem nome',
          userId: data.userId,
          isActive: data.isActive !== false,
          createdAt: data.createdAt?.toDate?.() || new Date(),
          updatedAt: data.updatedAt?.toDate?.() || new Date()
        };
      } catch (error) {
       
        return null;
      }
    },

    // ===== NOTES =====
    notes: async (_: any, { influencerId, userId }: { influencerId: string, userId: string }) => {
      try {
        if (!userId || userId === 'undefined' || userId === 'null' || !influencerId) {
          return [];
        }

        const snapshot = await db.collection('notes')
          .where('userId', '==', userId)
          .where('influencerId', '==', influencerId)
          .get();

        if (snapshot.empty) {
          return [];
        }

        const notes = snapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            title: data.title || 'Nota sem título',
            content: data.content || '',
            type: data.type || 'general',
            influencerId: data.influencerId || influencerId,
            userId: data.userId || userId,
            createdAt: data.createdAt?.toDate?.() || new Date(),
            updatedAt: data.updatedAt?.toDate?.() || new Date()
          };
        });

        return notes;
      } catch (error) {
       
        return [];
      }
    },

    allNotes: async (_: any, { userId }: { userId: string }) => {
      try {
        if (!userId || userId === 'undefined' || userId === 'null') {
          return [];
        }

        const snapshot = await db.collection('notes')
          .where('userId', '==', userId)
          .get();

        if (snapshot.empty) {
          return [];
        }

        const notes = snapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            title: data.title || 'Nota sem título',
            content: data.content || '',
            type: data.type || 'general',
            influencerId: data.influencerId || '',
            userId: data.userId || userId,
            createdAt: data.createdAt?.toDate?.() || new Date(),
            updatedAt: data.updatedAt?.toDate?.() || new Date()
          };
        });

        return notes;
      } catch (error) {
       
        return [];
      }
    },

    note: async (_: any, { id, userId }: { id: string, userId: string }) => {
      try {
        if (!userId || userId === 'undefined' || userId === 'null') {
          return null;
        }

        const doc = await db.collection('notes').doc(id).get();
        
        if (!doc.exists) {
          return null;
        }
        
        const data = doc.data();
        if (!data || data.userId !== userId) {
          return null;
        }
        
        return {
          id: doc.id,
          title: data.title || 'Nota sem título',
          content: data.content || '',
          type: data.type || 'general',
          influencerId: data.influencerId || '',
          userId: data.userId,
          createdAt: data.createdAt?.toDate?.() || new Date(),
          updatedAt: data.updatedAt?.toDate?.() || new Date()
        };
      } catch (error) {
        
        return null;
      }
    },

    // ===== BRANDS =====
    brands: async (_: any, { userId }: { userId: string }, context: GraphQLContext) => {
      try {
        // Verificar se userId é válido
        if (!userId || userId === 'undefined' || userId === 'null') {
          return [];
        }

        // Buscar marcas reais na coleção brands
        const snapshot = await db.collection('brands')
          .where('userId', '==', userId)
          .get();
          
        if (snapshot.empty) {
          return [];
        }
        
        // Processar marcas reais encontradas
        const brands = snapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            userId: data.userId || userId,
            name: data.name || 'Marca sem nome',
            description: data.description || '',
            logo: data.logo || '',
            website: data.website || '',
            industry: data.industry || '',
            size: data.size || '',
            status: data.status || 'active',
            createdAt: data.createdAt?.toDate?.() || new Date(),
            updatedAt: data.updatedAt?.toDate?.() || new Date()
          };
        });
        
        return brands;
        
      } catch (error) {
       
        const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
       
        
        // Em caso de erro, retornar array vazio para não quebrar a UI
        return [];
      }
    },

    brand: async (_: any, { id, userId }: { id: string, userId: string }) => {
      try {
        console.log(`🔒 [SECURITY] Buscando marca ${id} para userId: ${userId}`);

        // 🚨 VALIDAÇÃO DE SEGURANÇA: Exigir userId
        if (!userId || userId === 'undefined' || userId === 'null') {
          console.warn('⚠️ [SECURITY] Tentativa de buscar marca sem userId');
          return null;
        }

        const doc = await db.collection('brands').doc(id).get();

        if (!doc.exists) {
          return null;
        }

        const data = doc.data();
        if (!data) {
          return null;
        }

        // 🔒 VALIDAÇÃO: Verificar se a marca pertence ao usuário
        if (data.userId !== userId) {
          console.warn(`⚠️ [SECURITY] Tentativa de acesso a marca de outro usuário: ${id}`);
          return null;
        }

        console.log(`✅ [SECURITY] Marca ${id} acessada com sucesso pelo usuário: ${userId}`);
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate?.() || new Date(),
          updatedAt: data.updatedAt?.toDate?.() || new Date()
        };
      } catch (error) {
        console.error('❌ [SECURITY] Erro ao buscar marca:', error);
        throw new Error('Erro ao buscar marca');
      }
    },

    // ===== STATS OTIMIZADAS =====
    stats: async (_: any, { userId }: { userId: string }) => {
      try {
        if (!userId || userId === 'undefined' || userId === 'null') {
          // Retornar estatísticas padrão
          return {
            totalInfluencers: 0,
            totalViews: 0,
            totalFollowers: 0,
            totalBrands: 0,
            totalCampaigns: 0
          };
        }
        
        // 🚀 Usar serviço de cache otimizado
        const stats = await statsCache.getStats(userId);
        
        return {
          totalInfluencers: stats.totalInfluencers,
          totalViews: stats.totalViews,
          totalFollowers: stats.totalFollowers,
          totalBrands: stats.totalBrands,
          totalCampaigns: stats.totalCampaigns
        };
        
      } catch (error) {
        
        
        // Em caso de erro, retornar estatísticas padrão para não quebrar a UI
        return {
          totalInfluencers: 0,
          totalViews: 0,
          totalFollowers: 0,
          totalBrands: 0,
          totalCampaigns: 0
        };
      }
    },

    // ===== BRAND INFLUENCER ASSOCIATIONS =====
    brandInfluencerAssociations: async (parent: any, args: any, context: GraphQLContext) => {
      const { userId, influencerId, brandId, status } = args;
      
      // Validar argumentos obrigatórios
      if (!userId) {
       
        return [];
      }
      
      try {
        let query = db.collection('brand_influencers')
          .where('userId', '==', userId);
        
        if (influencerId) {
          query = query.where('influencerId', '==', influencerId);
        }
        
        if (brandId) {
          query = query.where('brandId', '==', brandId);
        }
        
        const snapshot = await query.get();
        
        if (snapshot.empty) {
          return [];
        }
        
        const associations = snapshot.docs.map(doc => {
          const data = doc.data();
          
          // Mapear campos flexivelmente baseado na estrutura real
          return {
            id: doc.id,
            userId: data.userId || userId,
            brandId: data.brandId || data.brand_id || '',
            brandName: data.brandName || data.brand_name || data.name || 'Nome não informado',
            brandLogo: data.brandLogo || data.brand_logo || data.logo || null,
            influencerId: data.influencerId || data.influencer_id || '',
            influencerName: data.influencerName || data.influencer_name || data.name || 'Nome não informado',
            influencerAvatar: data.influencerAvatar || data.influencer_avatar || data.avatar || null,
            status: data.status || 'active',
            tags: data.tags || [],
            notes: data.notes || null,
            createdAt: data.createdAt?.toDate?.() || data.created_at?.toDate?.() || new Date(),
            updatedAt: data.updatedAt?.toDate?.() || data.updated_at?.toDate?.() || new Date(),
            createdBy: data.createdBy || data.created_by || userId,
            updatedBy: data.updatedBy || data.updated_by || userId,
          };
        });
        
        return associations;
        
      } catch (error) {
       
        // SEMPRE retornar array vazio em caso de erro
        return [];
      }
    },

    // ===== SAVED FILTERS =====
    savedFilters: async (_: any, args: any, context: GraphQLContext) => {
      const { userId } = args;
      
      // Validar userId
      if (!userId || userId === 'undefined' || userId === 'null') {
        return [];
      }
      
      // VERSÃO DE TESTE: retornar dados mock primeiro
      const mockData = [
        {
          id: 'mock-1',
          userId: userId,
          name: 'Filtro de Teste',
          location: 'São Paulo - SP',
          minFollowers: 1000,
          maxFollowers: 100000,
          minRating: 4.0,
          verifiedOnly: false,
          availableOnly: true,
          platforms: {
            instagram: true,
            youtube: false,
            tiktok: false
          },
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      
      return mockData;
      
      // COMENTADO: Código original do Firestore (para debug)
      /*
      try {
        console.log(`🔍 [GraphQL] Executando query no Firestore`);
        
        const snapshot = await db.collection('savedFilters')
          .where('userId', '==', userId)
          .limit(10)
          .get();
        
        console.log(`📊 [GraphQL] Query executada - ${snapshot.size} documentos encontrados`);
        
        if (snapshot.empty) {
          console.log(`📭 [GraphQL] Nenhum filtro salvo encontrado para usuário ${userId}`);
          return [];
        }
        
        const filters = [];
        
        for (const doc of snapshot.docs) {
          try {
            const data = doc.data();
            
            if (!data) {
              console.log(`⚠️ [GraphQL] Documento ${doc.id} sem dados, pulando`);
              continue;
            }
            
            const filter = {
              id: doc.id,
              userId: data.userId || userId,
              name: data.name || 'Filtro sem nome',
              location: data.location || '',
              minFollowers: Number(data.minFollowers) || 0,
              maxFollowers: Number(data.maxFollowers) || 1000000,
              minRating: Number(data.minRating) || 0,
              verifiedOnly: Boolean(data.verifiedOnly),
              availableOnly: Boolean(data.availableOnly),
              platforms: {
                instagram: Boolean(data.platforms?.instagram),
                youtube: Boolean(data.platforms?.youtube),
                tiktok: Boolean(data.platforms?.tiktok)
              },
              createdAt: data.createdAt?.toDate?.() || new Date(),
              updatedAt: data.updatedAt?.toDate?.() || new Date()
            };
            
            filters.push(filter);
            console.log(`✅ [GraphQL] Filtro ${filter.name} processado`);
            
          } catch (docError) {
            console.error(`❌ [GraphQL] Erro ao processar documento ${doc.id}:`, docError);
            continue;
          }
        }
        
        console.log(`✅ [GraphQL] ${filters.length} filtros retornados`);
        return filters;
        
      } catch (error) {
        console.error(`❌ [GraphQL] Erro crítico ao buscar filtros salvos:`, error);
        console.error(`❌ [GraphQL] Stack trace:`, error instanceof Error ? error.stack : 'No stack trace');
        
        console.log(`🔄 [GraphQL] Retornando array vazio devido a erro`);
        return [];
      }
      */
    },

    savedFilter: async (_: any, { id, userId }: { id: string, userId: string }) => {
      try {
        console.log(`🔒 [SECURITY] Buscando filtro salvo ${id} para userId: ${userId}`);

        // 🚨 VALIDAÇÃO DE SEGURANÇA: Exigir userId
        if (!userId || userId === 'undefined' || userId === 'null') {
          console.warn('⚠️ [SECURITY] Tentativa de buscar filtro salvo sem userId');
          return null;
        }

        const doc = await db.collection('savedFilters').doc(id).get();

        if (!doc.exists) {
          return null;
        }

        const data = doc.data();
        if (!data) {
          return null;
        }

        // 🔒 VALIDAÇÃO: Verificar se o filtro pertence ao usuário
        if (data.userId !== userId) {
          console.warn(`⚠️ [SECURITY] Tentativa de acesso a filtro salvo de outro usuário: ${id}`);
          return null;
        }

        console.log(`✅ [SECURITY] Filtro salvo ${id} acessado com sucesso pelo usuário: ${userId}`);
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate?.() || new Date(),
          updatedAt: data.updatedAt?.toDate?.() || new Date()
        };
      } catch (error) {
        console.error('❌ [SECURITY] Erro ao buscar filtro salvo:', error);
        throw new Error('Erro ao buscar filtro salvo');
      }
    },

    // ===== PRICING QUERIES =====
    
    // Buscar pricing ativo de um influenciador (com validação de acesso)
    influencerPricing: async (_: any, { influencerId }: { influencerId: string }, context: GraphQLContext) => {
      try {
        const userId = context.user?.uid;
        if (!userId) {
          throw new Error('Usuário não autenticado');
        }
        
        return await getCurrentPricing(influencerId, userId);
      } catch (error) {
        
        throw new Error('Erro ao buscar pricing do influenciador');
      }
    },

    // Buscar histórico de pricing de um influenciador (com validação de acesso)
    influencerPricingHistory: async (_: any, { influencerId }: { influencerId: string }, context: GraphQLContext) => {
      try {
        const userId = context.user?.uid;
        if (!userId) {
          throw new Error('Usuário não autenticado');
        }
        
        return await getPricingHistory(influencerId, userId);
      } catch (error) {
       
        throw new Error('Erro ao buscar histórico de pricing');
      }
    },

    // ===== DEMOGRAPHICS QUERIES =====
    
    // Buscar demographics ativos de um influenciador
    influencerDemographics: async (_: any, { influencerId }: { influencerId: string }) => {
      try {
        const demographics = await getCurrentDemographics(influencerId);
        
        // Converter formato para GraphQL
        return demographics.map(demo => ({
          ...demo,
          audienceAgeRange: Object.entries(demo.audienceAgeRange).map(([range, percentage]) => ({
            range,
            percentage
          }))
        }));
      } catch (error) {
        
        throw new Error('Erro ao buscar demographics do influenciador');
      }
    },

    // Buscar demographics consolidados de um influenciador
    influencerConsolidatedDemographics: async (_: any, { influencerId }: { influencerId: string }) => {
      try {
        const { getConsolidatedDemographics } = await import('./firebase-demographics');
        return await getConsolidatedDemographics(influencerId);
      } catch (error) {
        
        throw new Error('Erro ao buscar demographics consolidados');
      }
    },

    // Buscar screenshots de um influenciador
    async influencerScreenshots(parent: any, { influencerId, platform }: { influencerId: string, platform?: string }, context: GraphQLContext) {
     
      
      try {
        // Validação de entrada
        if (!influencerId) {
         
          return [];
        }

        const screenshotsCollection = db
          .collection('influencers')
          .doc(influencerId)
          .collection('screenshots');

        let screenshots: any[] = [];

        if (platform) {
          // Buscar apenas uma plataforma específica
         
          const platformDoc = await screenshotsCollection.doc(platform).get();
          
          if (platformDoc.exists) {
            const data = platformDoc.data();
            const urls = data?.urls || [];
            
           
            
            // Converter cada URL em um objeto Screenshot completo
            urls.forEach((url: string, index: number) => {
              // Se for URL completa do Firebase Storage, usar diretamente
              // Se for caminho relativo, usar proxy
              let finalUrl: string;
              
              if (url.startsWith('https://storage.googleapis.com') || url.startsWith('https://firebasestorage.googleapis.com')) {
                // URL completa do Firebase Storage - usar diretamente
                finalUrl = url;
              } else {
                // Caminho relativo - usar proxy
                finalUrl = `/api/screenshots/${url}`;
              }
              
              // Extrair nome do arquivo da URL
              const urlParts = url.split('/');
              const filename = urlParts[urlParts.length - 1] || `screenshot_${index + 1}.jpg`;
              
              // ✅ Garantir que todos os campos obrigatórios têm valores válidos
              const uploadDate = (data?.lastUpdated && data.lastUpdated.toDate) 
                ? data.lastUpdated.toDate() 
                : new Date();
              
              screenshots.push({
                id: `${influencerId}_${platform}_${index}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                influencerId: influencerId,
                platform: platform,
                url: finalUrl,
                filename: filename,
                size: 0, // Int! obrigatório
                contentType: 'image/jpeg',
                uploadedAt: uploadDate, // Date! obrigatório
                uploadedBy: 'system'
              });
            });
          } else {
           
          }
        } else {
          // Buscar todas as plataformas
         
          const snapshot = await screenshotsCollection.get();
          
         
          
          if (!snapshot.empty) {
            snapshot.docs.forEach(doc => {
              const data = doc.data();
              const urls = data?.urls || [];
              const platformName = doc.id; // O ID do documento é o nome da plataforma
              
             
              
              // Converter cada URL em um objeto Screenshot completo
              urls.forEach((url: string, index: number) => {
                // Se for URL completa do Firebase Storage, usar diretamente
                // Se for caminho relativo, usar proxy
                let finalUrl: string;
                
                if (url.startsWith('https://storage.googleapis.com') || url.startsWith('https://firebasestorage.googleapis.com')) {
                  // URL completa do Firebase Storage - usar diretamente
                  finalUrl = url;
                } else {
                  // Caminho relativo - usar proxy
                  finalUrl = `/api/screenshots/${url}`;
                }
                
                // Extrair nome do arquivo da URL
                const urlParts = url.split('/');
                const filename = urlParts[urlParts.length - 1] || `screenshot_${index + 1}.jpg`;
                
                // ✅ Garantir que todos os campos obrigatórios têm valores válidos
                const uploadDate = (data?.lastUpdated && data.lastUpdated.toDate) 
                  ? data.lastUpdated.toDate() 
                  : new Date();
                
                const screenshotObj = {
                  id: `${influencerId}_${platformName}_${index}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                  influencerId: influencerId,
                  platform: platformName,
                  url: finalUrl,
                  filename: filename,
                  size: 0, // Int! obrigatório
                  contentType: 'image/jpeg',
                  uploadedAt: uploadDate, // Date! obrigatório
                  uploadedBy: 'system'
                };
                
               
                screenshots.push(screenshotObj);
              });
            });
          } else {
           
          }
        }

       
        return screenshots;
        
      } catch (error) {
        
        // ✅ IMPORTANTE: Retornar array vazio em caso de erro, não throw
        // Isso evita o erro "Cannot return null for non-nullable field"
        return [];
      }
    },

    // ===== 🆕 NOVOS RESOLVERS DE ORÇAMENTOS COM CONTROLE DE ACESSO =====

    // 📊 BUSCAR TODOS OS ORÇAMENTOS DE UMA PROPOSTA
    proposalBudgets: async (parent: any, { proposalId, userId }: { proposalId: string, userId: string }, context: GraphQLContext) => {
     
      
      try {
        const { BudgetService } = await import('@/services/budget-service');
        return await BudgetService.getProposalBudgets(proposalId, userId);
      } catch (error) {
       
        return {
          proposalId,
          permissions: {
            canView: false,
            canEdit: false,
            canCreateCounterProposal: false,
            canManageBudgets: false,
            canApproveCounterProposals: false,
            canViewFinancialData: false
          },
          budgets: [],
          totalCount: 0,
          userRole: '',
          errors: [error instanceof Error ? error.message : 'Erro interno'],
          processingTimeMs: 0
        };
      }
    },

    // 🎯 BUSCAR ORÇAMENTOS DE UM INFLUENCIADOR ESPECÍFICO EM UMA PROPOSTA
    influencerBudgetsInProposal: async (parent: any, { proposalId, influencerId, userId }: { proposalId: string, influencerId: string, userId: string }, context: GraphQLContext) => {
     
      
      try {
        const { BudgetService } = await import('@/services/budget-service');
        return await BudgetService.getInfluencerBudgetsInProposal(proposalId, influencerId, userId);
      } catch (error) {
       
        return {
          proposalId,
          permissions: {
            canView: false,
            canEdit: false,
            canCreateCounterProposal: false,
            canManageBudgets: false,
            canApproveCounterProposals: false,
            canViewFinancialData: false
          },
          budgets: [],
          totalCount: 0,
          userRole: '',
          errors: [error instanceof Error ? error.message : 'Erro interno'],
          processingTimeMs: 0
        };
      }
    },

    // 🔍 BUSCAR ORÇAMENTOS POR IDs COM VERIFICAÇÃO DE ACESSO
    budgetsByIds: async (parent: any, { budgetIds, userId, proposalId }: { budgetIds: string[], userId: string, proposalId?: string }, context: GraphQLContext) => {
     
      
      try {
        // Se tiver proposalId, usar verificação de acesso de proposta
        if (proposalId) {
          const { BudgetService } = await import('@/services/budget-service');
          const accessResult = await BudgetService.canUserAccessBudgets(proposalId, userId);
          
          if (!accessResult.hasAccess) {
           
            return [];
          }
        }

        // Buscar orçamentos por IDs (implementação básica por enquanto)
        // TODO: Implementar busca otimizada por múltiplos IDs
        const budgets: any[] = [];
        
        for (const budgetId of budgetIds) {
          try {
            // Buscar em todas as propostas do usuário
            // Esta é uma implementação temporária - pode ser otimizada
            const querySnapshot = await db.collectionGroup('budgets')
              .where('userId', '==', userId)
              .get();
              
            querySnapshot.docs.forEach(doc => {
              if (budgetIds.includes(doc.id)) {
                const data = doc.data();
                budgets.push({
                  id: doc.id,
                  ...data,
                  createdAt: data.createdAt?.toDate?.() || data.createdAt,
                  updatedAt: data.updatedAt?.toDate?.() || data.updatedAt,
                  expiresAt: data.expiresAt?.toDate?.() || data.expiresAt
                });
              }
            });
          } catch (error) {
           
          }
        }

       
        return budgets;
        
      } catch (error) {
       
        return [];
      }
    },

    // 💼 CONTRAPROPOSTAS DE COLABORADORES
    collaboratorCounterProposals: async (parent: any, { proposalId, userId, status }: { proposalId: string, userId: string, status?: string }, context: GraphQLContext) => {
     
      
      try {
        const { BudgetService } = await import('@/services/budget-service');
        return await BudgetService.getCollaboratorCounterProposals(proposalId, userId, status as any);
      } catch (error) {
       
        return [];
      }
    },

    // 💰 ORÇAMENTOS INDEPENDENTES (NÃO VINCULADOS A PROPOSTAS)
    independentBudgets: async (parent: any, { userId, brandId, status, serviceType }: { 
      userId: string, 
      brandId?: string, 
      status?: string, 
      serviceType?: string 
    }, context: GraphQLContext) => {
     
      
      try {
        // Buscar orçamentos independentes diretamente no Firestore
        let budgetsQuery = db.collection('budgets')
          .where('userId', '==', userId)
          .where('proposalId', '==', null); // Orçamentos não vinculados a propostas
        
        // Aplicar filtros opcionais
        if (brandId) {
          budgetsQuery = budgetsQuery.where('brandId', '==', brandId);
        }
        if (status) {
          budgetsQuery = budgetsQuery.where('status', '==', status);
        }
        if (serviceType) {
          budgetsQuery = budgetsQuery.where('serviceType', '==', serviceType);
        }
        
        const budgetsSnapshot = await budgetsQuery.orderBy('createdAt', 'desc').get();
        
        const independentBudgets = budgetsSnapshot.docs.map((doc: any) => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate ? doc.data().createdAt.toDate() : doc.data().createdAt,
          updatedAt: doc.data().updatedAt?.toDate ? doc.data().updatedAt.toDate() : doc.data().updatedAt
        }));
        
        
        
        return independentBudgets;
      } catch (error) {
        
        return [];
      }
    },

    // 🎯 BUSCAR STATUS DOS INFLUENCERS NA PROPOSTA
    proposalInfluencersStatus: async (parent: any, { proposalId, userId }: { proposalId: string, userId: string }, context: GraphQLContext) => {
     
      
      try {
        // Verificar se o usuário tem acesso à proposta
        const proposalRef = db.collection('proposals').doc(proposalId);
        const proposalDoc = await proposalRef.get();
        
        if (!proposalDoc.exists) {
          
          return [];
        }
        
        const proposalData = proposalDoc.data();
        
        // 🔧 CORREÇÃO: Verificação de acesso aprimorada
        let hasAccess = false;
        
        // 1. Verificar se o usuário é proprietário
        const isOwner = proposalData?.criadoPor === userId;
        if (isOwner) {
          hasAccess = true;
          
        }
        
        // 2. Verificar se é colaborador direto na proposta
        if (!hasAccess) {
          const isCollaborator = proposalData?.collaborators?.some((c: any) => 
            c.userId === userId && c.status === 'active'
          );
          if (isCollaborator) {
            hasAccess = true;
            
          }
        }
        
                 // 3. 🆕 CORREÇÃO PRINCIPAL: Verificar acesso via Clerk metadata (convites/compartilhamentos)
         if (!hasAccess) {
           try {
             const { clerkClient } = await import('@clerk/nextjs/server') as any;
             const clerk = await clerkClient();
             const user = await clerk.users.getUser(userId);
            
            const proposalAccess = (user.unsafeMetadata as any)?.p?.[proposalId];
            
            // Verificar se tem acesso via metadata (a = admin, e = editor, v = viewer)
            if (proposalAccess && ['a', 'e', 'v'].includes(proposalAccess)) {
              hasAccess = true;
             
            } else {
             
            }
            
          } catch (clerkError) {
           
          }
        }
        
                 // 4. 🆕 FALLBACK: Verificar se o usuário tem acesso indireto (ex: via organização)
         if (!hasAccess && proposalData?.organizationId) {
           try {
             const { clerkClient } = await import('@clerk/nextjs/server') as any;
             const clerk = await clerkClient();
             const user = await clerk.users.getUser(userId);
            
            // Verificar se o usuário pertence à mesma organização
            const userOrgId = (user.organizationMemberships || []).find((membership: any) => 
              membership.organization.id === proposalData.organizationId
            );
            
            if (userOrgId) {
              hasAccess = true;
             
            }
            
          } catch (orgError) {
           
          }
        }
        
        // 🚨 DECISÃO FINAL: Negar acesso apenas se nenhuma verificação passou
        if (!hasAccess) {
          
          return [];
        }
        
        // 🎯 BUSCAR STATUS DOS INFLUENCERS (só executa se tem acesso)
       
        
        const influencersSnapshot = await db
          .collection('proposals')
          .doc(proposalId)
          .collection('influencers')
          .get();
        
        const influencersStatus = influencersSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            influencerId: doc.id,
            status: data.status || 'pendente',
            addedAt: data.addedAt?.toDate?.() || data.addedAt,
            updatedAt: data.updatedAt?.toDate?.() || data.updatedAt,
            addedBy: data.addedBy,
            updatedBy: data.updatedBy
          };
        });
        
        
        
        return influencersStatus;
      } catch (error) {
        
        return [];
      }
    }
  },

  // ===== NESTED RESOLVERS =====
  Influencer: {
    // Screenshots
    async screenshots(parent: any) {
      const influencerId = parent.id;
      
      try {
        const screenshotsRef = db.collection('influencers')
          .doc(influencerId)
          .collection('screenshots');
        
        const snapshot = await screenshotsRef.get();
        
        if (snapshot.empty) {
          return [];
        }
        
        const allScreenshots: any[] = [];
        
        snapshot.docs.forEach(doc => {
          const data = doc.data();
          const platform = doc.id;
          const urls = data.urls || [];
          
          urls.forEach((url: string, index: number) => {
            allScreenshots.push({
              id: `${influencerId}_${platform}_${index}`,
              influencerId,
              platform,
              url,
              filename: `screenshot_${platform}_${index}.jpg`,
              size: 0,
              contentType: 'image/jpeg',
              uploadedAt: data.lastUpdated || new Date(),
              uploadedBy: 'system'
            });
          });
        });
        
        return allScreenshots;
      } catch (error) {
       
        return [];
      }
    },

    // Orçamentos organizados por plataforma
    async budgets(parent: any, args: any, context: GraphQLContext) {
      const influencerId = parent.id;
      
      
      
      try {
        // Buscar orçamentos na nova estrutura: influencers/{influencerId}/budgets/{platform}
        const budgetsRef = db
          .collection('influencers')
          .doc(influencerId)
          .collection('budgets');
        
        
        
        const budgetsSnapshot = await budgetsRef.get();
        
        
        
                 // Organizar orçamentos por plataforma
         const budgetsByPlatform: { [key: string]: any[] } = {
           instagram: [],
           tiktok: [],
           youtube: [],
           facebook: [],
           twitch: [],
           kwai: [],
           personalizado: []
         };
         
         budgetsSnapshot.docs.forEach((doc, index) => {
           const platform = doc.id; // Nome do documento é a plataforma
           const budgetData = doc.data();
           
           
           
           const budget = {
             id: doc.id,
             ...budgetData,
             createdAt: budgetData.createdAt?.toDate?.() || new Date(),
             updatedAt: budgetData.updatedAt?.toDate?.() || new Date()
           };
           
           // Adicionar ao array da plataforma correspondente
           if (budgetsByPlatform.hasOwnProperty(platform)) {
             budgetsByPlatform[platform].push(budget);
             
           } else {
             // Se a plataforma não estiver mapeada, adicionar em personalizado
             budgetsByPlatform.personalizado.push(budget);
             
           }
         });
        

        
       
        
        return budgetsByPlatform;
        
             } catch (error) {
        
        
        // Retornar estrutura vazia em caso de erro
        return {
          instagram: [],
          tiktok: [],
          youtube: [],
          facebook: [],
          twitch: [],
          kwai: [],
          personalizado: []
        };
      }
    },

    // Dados financeiros
    async financial(parent: any) {
      if (!parent.id) return null;
      
      try {
        return await FinancialCacheService.getFinancialData(parent.id);
      } catch (error) {
        
        return null;
      }
    },

    // Resolver para pricing atual (com validação de acesso)
    async currentPricing(parent: any, _: any, context: GraphQLContext) {
      if (!parent.id) {
        return null;
      }
      
      const userId = context.user?.uid;
      if (!userId) {
        return null;
      }
      
      try {
        const result = await getCurrentPricing(parent.id, userId);
        
        if (result) {
         
        }
        
        return result;
      } catch (error) {
       
        
        // Se for erro de acesso, retornar null silenciosamente
        if (error instanceof Error && error.message.includes('Acesso negado')) {
          return null;
        }
        
        return null;
      }
    },

    // Resolver para histórico de pricing (com validação de acesso)
    async pricingHistory(parent: any, _: any, context: GraphQLContext) {
      if (!parent.id) return [];
      
      const userId = context.user?.uid;
      if (!userId) {
        return [];
      }
      
      try {
        return await getPricingHistory(parent.id, userId);
      } catch (error) {
       
        
        // Se for erro de acesso, retornar array vazio silenciosamente
        if (error instanceof Error && error.message.includes('Acesso negado')) {
          return [];
        }
        
        return [];
      }
    },

    // Resolver para demographics atuais
    async currentDemographics(parent: any) {
      
      if (!parent.id) {
       
        return [];
      }
      
     
      
      try {
        const demographics = await getCurrentDemographics(parent.id);
       
        
        // Converter formato para GraphQL
        const converted = demographics.map(demo => ({
          ...demo,
          audienceAgeRange: Object.entries(demo.audienceAgeRange).map(([range, percentage]) => ({
            range,
            percentage
          }))
        }));
        
        return converted;
      } catch (error) {
       
        return [];
      }
    },

    // Resolver para demographics consolidados
    async consolidatedDemographics(parent: any) {
      if (!parent.id) return null;
      
     
      
      try {
        const { getConsolidatedDemographics } = await import('./firebase-demographics');
        return await getConsolidatedDemographics(parent.id);
      } catch (error) {
       
        return null;
      }
    },

    // Resolver para campanhas - placeholder
    async campaigns() {
      return [];
    },

    // Resolver para marcas - placeholder
    async brands() {
      return [];
    },

    // Resolver para tags - placeholder
    async tags() {
      return [];
    },

    // Resolver para notes - placeholder
    async notes() {
      return [];
    },

    // Resolver para categorias principais - placeholder
    async mainCategoriesData() {
      return [];
    }
  },

  // ===== MUTATIONS =====
  Mutation: {
    // Criar influenciador
    async createInfluencer(parent: any, { input }: any, context: GraphQLContext) {
     
      
      // 🔍 Log dos dados diretos das plataformas recebidos
      if (input.instagramUsername) {
        console.log('Instagram data:', {
          username: input.instagramUsername,
          followers: input.instagramFollowers,
          engagement: input.instagramEngagementRate
        });
      }
      if (input.tiktokUsername) {
        console.log('TikTok data:', {
          username: input.tiktokUsername,
          followers: input.tiktokFollowers,
          engagement: input.tiktokEngagementRate
        });
      }
      if (input.youtubeUsername) {
        console.log('YouTube data:', {
          username: input.youtubeUsername,
          followers: input.youtubeFollowers,
          engagement: input.youtubeEngagementRate
        });
      }
      
      try {
        // 🔥 CORREÇÃO CRÍTICA: Adicionar userId aos dados do influencer com verificação
        const userId = context.user?.uid;
        
        if (!userId) {
         
          throw new Error('Usuário não autenticado. Faça login para continuar.');
        }
        
       
       
        
        // ✅ Calcular totalFollowers e totalViews automaticamente se não fornecidos
        const calculatedTotalFollowers = input.totalFollowers || calculateTotalFollowers(input);
        const calculatedTotalViews = input.totalViews || calculateTotalViews(input);
        


        const influencerDataWithUserId = {
          ...input,
          userId: userId,
          totalFollowers: calculatedTotalFollowers,
          totalViews: calculatedTotalViews,
          // 🔥 CORREÇÃO: Garantir que responsibleCapturer seja salvo se fornecido
          responsibleCapturer: input.responsibleCapturer || null
        };
        
        const influencer = await addInfluencer(influencerDataWithUserId);
        
        
        // ✅ Dados das redes sociais agora são campos diretos no documento principal
        
       
        
                // 💰 Pricing e 📊 Demographics agora são criados via mutations separadas no frontend
        
        // Criar dados financeiros simplificados (sem pricing)
        if (input.financialData) {
          
          
          const financialData = {
                influencerId: influencer.id,
            responsibleName: input.financialData?.responsibleName || input.name || 'Não informado',
            agencyName: input.financialData?.agencyName || '',
            email: input.financialData?.email || input.email || '',
            whatsapp: input.financialData?.whatsapp || input.whatsapp || ''
          };
          
          await addFinancial(financialData);
          
        }
        
        // 🚀 Atualizar contador de estatísticas
        await statsCache.updateStatsCounter(
          userId, 
          'add', 
          'influencer', 
          { 
            totalFollowers: calculatedTotalFollowers,
            totalViews: calculatedTotalViews
          }
        );

        // Publish para subscription
        pubsub.publish('INFLUENCER_CREATED', {
          influencerCreated: influencer,
          userId: context.user?.uid
        });
        
        return influencer;
      } catch (error) {
        
        throw new Error('Erro ao criar influenciador');
      }
    },

    // Atualizar influenciador
    async updateInfluencer(parent: any, { id, input }: any, context: GraphQLContext) {
     
      
      // 🔍 LOG ESPECÍFICO PARA CAMPOS DE VISUALIZAÇÕES
      
      try {
        const userId = context.user?.uid;
        
        if (!userId) {
          throw new Error('Usuário não autenticado. Faça login para continuar.');
        }
        
        // 🔒 VERIFICAÇÃO DE SEGURANÇA: Verificar se o influenciador pertence ao usuário
        const existingInfluencer = await getInfluencerById(id);
        if (!existingInfluencer) {
          throw new Error('Influenciador não encontrado');
        }
        
        // Permitir edição se for o dono OU se for dados do sistema (para compatibilidade)
        if (existingInfluencer.userId !== userId && existingInfluencer.userId !== 'system' && userId !== 'system') {
         
          throw new Error('Você não tem permissão para editar este influenciador');
        }
        
       
        
        // ✅ Calcular totalFollowers e totalViews automaticamente se necessário
        const calculatedTotalFollowers = input.totalFollowers !== undefined ? input.totalFollowers : 
          (existingInfluencer.totalFollowers || calculateTotalFollowers({ ...existingInfluencer, ...input }));
        const calculatedTotalViews = input.totalViews !== undefined ? input.totalViews : 
          (existingInfluencer.totalViews || calculateTotalViews({ ...existingInfluencer, ...input }));

        const updatedData = {
          // Incluir TODOS os campos do input de atualização
          ...input,
          // Garantir que campos opcionais sejam preservados se não fornecidos
          phone: input.phone !== undefined ? input.phone : existingInfluencer.phone,
          whatsapp: input.whatsapp !== undefined ? input.whatsapp : existingInfluencer.whatsapp,
          bio: input.bio !== undefined ? input.bio : existingInfluencer.bio,
          totalFollowers: calculatedTotalFollowers,
          totalViews: calculatedTotalViews,
          engagementRate: input.engagementRate !== undefined ? input.engagementRate : existingInfluencer.engagementRate,
          // 🔥 CORREÇÃO: Garantir que responsibleCapturer seja preservado se fornecido
          responsibleCapturer: input.responsibleCapturer !== undefined ? input.responsibleCapturer : existingInfluencer.responsibleCapturer,
          updatedAt: new Date().toISOString()
        };
        
        // 🔍 LOG DOS DADOS QUE SERÃO ENVIADOS PARA O FIREBASE
       
        
        const influencer = await updateInfluencer(id, updatedData);
        
        // ✅ Dados das redes sociais já atualizados como campos diretos
        
        
        
        
        return influencer;
      } catch (error) {
       
        throw new Error(error instanceof Error ? error.message : 'Erro ao atualizar influenciador');
      }
    },

    // Deletar influenciador
    async deleteInfluencer(parent: any, { id }: { id: string }, context: GraphQLContext) {
     
      
      try {
        const userId = context.user?.uid;
        
        if (!userId) {
          throw new Error('Usuário não autenticado. Faça login para continuar.');
        }
        
        // 🔒 VERIFICAÇÃO DE SEGURANÇA: Verificar se o influenciador pertence ao usuário
        const existingInfluencer = await getInfluencerById(id);
        if (!existingInfluencer) {
          throw new Error('Influenciador não encontrado');
        }
        
        // Permitir deleção se for o dono OU se for dados do sistema (para compatibilidade)
        if (existingInfluencer.userId !== userId && existingInfluencer.userId !== 'system' && userId !== 'system') {
         
          throw new Error('Você não tem permissão para deletar este influenciador');
        }
        
       
        
        // 🚀 Atualizar contador de estatísticas ANTES de deletar
        await statsCache.updateStatsCounter(
          userId, 
          'remove', 
          'influencer', 
          { 
            totalFollowers: existingInfluencer.totalFollowers || 0,
            totalViews: existingInfluencer.totalViews || 0
          }
        );

        await deleteInfluencer(id);
        
        // Limpar cache
        FinancialCacheService.invalidateInfluencer(id);
        
        
        return true;
      } catch (error) {
       
        throw new Error(error instanceof Error ? error.message : 'Erro ao deletar influenciador');
      }
    },

    // Sincronizar dados financeiros denormalizados
    async syncInfluencerFinancialData(parent: any, { influencerId }: { influencerId: string }, context: GraphQLContext) {
     
      
      try {
        const success = await FinancialDenormalizationService.updateInfluencerPricing(influencerId);
        return success;
      } catch (error) {
       
        throw new Error('Erro ao sincronizar dados financeiros');
      }
    },

    // Limpar cache
    async clearCache() {
     
      
      try {
        FinancialCacheService.clearCache();
        return true;
      } catch (error) {
       
        throw new Error('Erro ao limpar cache');
      }
    },

    // ===== SAVED FILTERS MUTATIONS =====
    
    // Criar filtro salvo
    async createSavedFilter(parent: any, { input }: any, context: GraphQLContext) {
      try {
       
        
        const userId = context.user?.uid;
        
        if (!userId) {
          throw new Error('Usuário não autenticado. Faça login para continuar.');
        }
        
        const filterData = {
          ...input,
          userId,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        
        
        const docRef = await db.collection('savedFilters').add(filterData);
        const doc = await docRef.get();
        const data = doc.data();
        
        if (!data) {
          throw new Error('Erro ao recuperar filtro salvo criado');
        } 
        
        const savedFilter = {
          id: doc.id,
          userId: data.userId,
          name: data.name,
          location: data.location || '',
          minFollowers: data.minFollowers || 0,
          maxFollowers: data.maxFollowers || 1000000,
          minRating: data.minRating || 0,
          verifiedOnly: data.verifiedOnly || false,
          availableOnly: data.availableOnly || false,
          platforms: data.platforms || {
            instagram: false,
            youtube: false,
            tiktok: false
          },
          createdAt: data.createdAt?.toDate?.() || new Date(),
          updatedAt: data.updatedAt?.toDate?.() || new Date()
        };
        
        
        return savedFilter;
      } catch (error) {
       
        throw new Error('Erro ao criar filtro salvo');
      }
    },

    // Atualizar filtro salvo
    async updateSavedFilter(parent: any, { id, input }: any, context: GraphQLContext) {
      try {
        
        
        const userId = context.user?.uid || 'system';
        
        // Verificar se o filtro existe e pertence ao usuário
        const docRef = db.collection('savedFilters').doc(id);
        const doc = await docRef.get();
        
        if (!doc.exists) {
          throw new Error('Filtro salvo não encontrado');
        }
        
        const existingData = doc.data();
        if (existingData?.userId !== userId && userId !== 'system') {
          throw new Error('Você não tem permissão para editar este filtro');
        }
        
        const updateData = {
          ...input,
          updatedAt: new Date()
        };
        
        await docRef.update(updateData);
        
        // Buscar dados atualizados
        const updatedDoc = await docRef.get();
        const data = updatedDoc.data();
        
        if (!data) {
          throw new Error('Erro ao recuperar filtro salvo atualizado');
        }
        
        const savedFilter = {
          id: updatedDoc.id,
          userId: data.userId,
          name: data.name,
          location: data.location || '',
          minFollowers: data.minFollowers || 0,
          maxFollowers: data.maxFollowers || 1000000,
          minRating: data.minRating || 0,
          verifiedOnly: data.verifiedOnly || false,
          availableOnly: data.availableOnly || false,
          platforms: data.platforms || {
            instagram: false,
            youtube: false,
            tiktok: false
          },
          createdAt: data.createdAt?.toDate?.() || new Date(),
          updatedAt: data.updatedAt?.toDate?.() || new Date()
        };
        
        
        return savedFilter;
      } catch (error) {
       
        throw new Error(error instanceof Error ? error.message : 'Erro ao atualizar filtro salvo');
      }
    },

    // Deletar filtro salvo
    async deleteSavedFilter(parent: any, { id }: { id: string }, context: GraphQLContext) {
      try {
        
        
        const userId = context.user?.uid || 'system';
        
        // Verificar se o filtro existe e pertence ao usuário
        const docRef = db.collection('savedFilters').doc(id);
        const doc = await docRef.get();
        
        if (!doc.exists) {
          throw new Error('Filtro salvo não encontrado');
        }
        
        const existingData = doc.data();
        if (existingData?.userId !== userId && userId !== 'system') {
          throw new Error('Você não tem permissão para deletar este filtro');
        }
        
        await docRef.delete();
        
        
        return true;
      } catch (error) {
       
        throw new Error(error instanceof Error ? error.message : 'Erro ao deletar filtro salvo');
      }
    },

    // ===== PRICING MUTATIONS =====
    
    // Criar pricing para influenciador
    async createInfluencerPricing(parent: any, { input }: any, context: GraphQLContext) {
      try {
        
        
        
        const userId = context.user?.uid || 'system';
        
        const pricingData = {
          influencerId: input.influencerId,
          userId: userId,
          services: input.services,
          isActive: true,
          validFrom: input.validFrom ? new Date(input.validFrom) : new Date(),
          validUntil: input.validUntil ? new Date(input.validUntil) : undefined,
          notes: input.notes || '',
          clientSpecific: input.clientSpecific || '',
          createdBy: userId,
          updatedBy: userId
        };
        
        const pricingId = await createPricing(pricingData, userId);
        
        // Buscar o pricing criado para retornar (sem validação aqui pois é o próprio criador)
        const createdPricing = await getCurrentPricing(input.influencerId);
        
        
        return createdPricing;
      } catch (error) {
       
        throw new Error('Erro ao criar pricing do influenciador');
      }
    },

    // Atualizar pricing existente
    async updateInfluencerPricing(parent: any, { id, input }: any, context: GraphQLContext) {
      try {
        
        
        
        const userId = context.user?.uid || 'system';
        
        // Atualizar com nova assinatura (influencerId, pricingId, updateData, userId)
        await updatePricing(input.influencerId, id, input, userId);
        
        // Buscar o pricing atualizado para retornar (sem validação aqui pois é o próprio editor)
        const updatedPricing = await getCurrentPricing(input.influencerId);
        
        
        return updatedPricing;
      } catch (error) {

        throw new Error('Erro ao atualizar pricing do influenciador');
      }
    },

    // Deletar pricing
    async deleteInfluencerPricing(parent: any, { id, influencerId }: { id: string, influencerId: string }, context: GraphQLContext) {
      try {
        
        
        // Deletar com nova assinatura (influencerId, pricingId)
        await deletePricing(influencerId, id);
        

        return true;
      } catch (error) {
       
        throw new Error('Erro ao deletar pricing do influenciador');
      }
    },

    // ===== DEMOGRAPHICS MUTATIONS =====
    
    // Criar demographic para influenciador
    async createAudienceDemographic(parent: any, { input }: any, context: GraphQLContext) {
      try {
       
        
        const userId = context.user?.uid || 'system';
        
        // Converter audienceAgeRange de array para objeto
        const audienceAgeRangeObj: { [key: string]: number } = {};
        if (input.audienceAgeRange && Array.isArray(input.audienceAgeRange)) {
          input.audienceAgeRange.forEach((item: any) => {
            if (item && item.range && typeof item.percentage === 'number') {
              audienceAgeRangeObj[item.range] = item.percentage;
            }
          });
        }
        
       
        
        const demographicData: Omit<AudienceDemographic, 'id' | 'createdAt' | 'updatedAt'> = {
          influencerId: input.influencerId,
          userId: userId,
          platform: input.platform,
          audienceGender: input.audienceGender,
          audienceLocations: input.audienceLocations,
          audienceCities: input.audienceCities,
          audienceAgeRange: audienceAgeRangeObj,
          captureDate: input.captureDate ? new Date(input.captureDate) : new Date(),
          isActive: true,
          source: input.source || 'form',
          createdBy: userId,
          updatedBy: userId
        };
        
       
        
        const demographicId = await createDemographic(demographicData, userId);
        
        // Buscar o demographic criado para retornar com nova assinatura
        const { getDemographicById } = await import('./firebase-demographics');
        const createdDemographic = await getDemographicById(input.influencerId, demographicId);
        
        if (createdDemographic) {
          // Converter formato para GraphQL
          const result = {
            ...createdDemographic,
            audienceAgeRange: Object.entries(createdDemographic.audienceAgeRange).map(([range, percentage]) => ({
              range,
              percentage
            }))
          };
          
         
          return result;
        }
        
        throw new Error('Erro ao recuperar demographic criado');
      } catch (error) {
       
        throw new Error('Erro ao criar demographic do influenciador');
      }
    },

    // Atualizar demographic existente
    async updateAudienceDemographic(parent: any, { id, input }: any, context: GraphQLContext) {
      try {
       
        
        const userId = context.user?.uid || 'system';
        
        // Converter audienceAgeRange se fornecido
        let updateData: any = { ...input };
        if (input.audienceAgeRange && Array.isArray(input.audienceAgeRange)) {
          const audienceAgeRangeObj: { [key: string]: number } = {};
          input.audienceAgeRange.forEach((item: any) => {
            if (item && item.range && typeof item.percentage === 'number') {
              audienceAgeRangeObj[item.range] = item.percentage;
            }
          });
          updateData.audienceAgeRange = audienceAgeRangeObj;
          
         
        }
        
        const { updateDemographic, getDemographicById } = await import('./firebase-demographics');
        // Atualizar com nova assinatura (influencerId, demographicId, updateData, userId)
        await updateDemographic(input.influencerId, id, updateData, userId);
        
        // Buscar o demographic atualizado para retornar com nova assinatura
        const updatedDemographic = await getDemographicById(input.influencerId, id);
        
        if (updatedDemographic) {
          // Converter formato para GraphQL
          const result = {
            ...updatedDemographic,
            audienceAgeRange: Object.entries(updatedDemographic.audienceAgeRange).map(([range, percentage]) => ({
              range,
              percentage
            }))
          };
          
         
          return result;
        }
        
        throw new Error('Erro ao recuperar demographic atualizado');
      } catch (error) {
       
        throw new Error('Erro ao atualizar demographic do influenciador');
      }
    },

    // Deletar demographic
    async deleteAudienceDemographic(parent: any, { id, influencerId }: { id: string, influencerId: string }, context: GraphQLContext) {
      try {
       
        
        const { deleteDemographic } = await import('./firebase-demographics');
        // Deletar com nova assinatura (influencerId, demographicId)
        await deleteDemographic(influencerId, id);
        
       
        return true;
      } catch (error) {
       
        throw new Error('Erro ao deletar demographic do influenciador');
      }
    },

    // ===== BRAND INFLUENCER ASSOCIATIONS =====
    async createBrandInfluencerAssociation(parent: any, { input }: any, context: GraphQLContext) {
     
      
      try {
        // Buscar dados da marca e do influenciador
        const [brandSnap, influencerSnap] = await Promise.all([
          db.collection('brands').doc(input.brandId).get(),
          db.collection('influencers').doc(input.influencerId).get()
        ]);
        
        if (!brandSnap.exists) {
          throw new Error('Marca não encontrada');
        }
        
        if (!influencerSnap.exists) {
          throw new Error('Influenciador não encontrado');
        }
        
        const brandData = brandSnap.data()!;
        const influencerData = influencerSnap.data()!;
        
        // Verificar se já existe associação na coleção correta
        const existingQuery = await db.collection('brand_influencers')
          .where('userId', '==', input.userId)
          .where('brandId', '==', input.brandId)
          .where('influencerId', '==', input.influencerId)
          .get();
        
        if (!existingQuery.empty) {
          // Se já existe, retornar a associação existente ao invés de erro
          const existingDoc = existingQuery.docs[0];
          const existingData = existingDoc.data();
          
         
          
          return {
            id: existingDoc.id,
            userId: existingData.userId,
            brandId: existingData.brandId,
            brandName: existingData.brandName || brandData.name,
            brandLogo: existingData.brandLogo || brandData.logo,
            influencerId: existingData.influencerId,
            influencerName: existingData.influencerName || influencerData.name,
            influencerAvatar: existingData.influencerAvatar || influencerData.avatar,
            status: existingData.status || 'active',
            tags: existingData.tags || [],
            notes: existingData.notes || null,
            createdAt: existingData.createdAt?.toDate?.() || new Date(),
            updatedAt: existingData.updatedAt?.toDate?.() || new Date(),
            createdBy: existingData.createdBy || input.userId,
            updatedBy: existingData.updatedBy || input.userId
          };
        }
        
        const associationData = {
          userId: input.userId,
          brandId: input.brandId,
          brandName: brandData.name,
          brandLogo: brandData.logo || null,
          influencerId: input.influencerId,
          influencerName: influencerData.name,
          influencerAvatar: influencerData.avatar || null,
          status: input.status || 'active',
          tags: input.tags || [],
          notes: input.notes || null,
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: input.userId,
          updatedBy: input.userId
        };
        
        // CORREÇÃO: Usar a coleção correta 'brand_influencers'
        const docRef = await db.collection('brand_influencers').add(associationData);
        
        const result = {
          id: docRef.id,
          ...associationData
        };
        
       
        return result;
        
      } catch (error) {
       
        throw new Error(error instanceof Error ? error.message : 'Erro ao criar associação');
      }
    },

    async deleteBrandInfluencerAssociation(parent: any, { id }: { id: string }, context: GraphQLContext) {
     
      
      try {
        // Verificar se existe na coleção correta
        const associationDoc = await db.collection('brand_influencers').doc(id).get();
        
        if (!associationDoc.exists) {
          throw new Error('Associação não encontrada na coleção brand_influencers');
        }
        
        // Deletar da coleção correta
        await db.collection('brand_influencers').doc(id).delete();
        
       
        return true;
        
      } catch (error) {
       
        throw new Error(error instanceof Error ? error.message : 'Erro ao deletar associação');
      }
    },

    // ===== BRANDS MUTATIONS =====
    async createBrand(parent: any, { input }: any, context: GraphQLContext) {
     
      
      try {
        const userId = context.user?.uid || input.userId;
        
        if (!userId) {
          throw new Error('Usuário não autenticado');
        }
        
        // Verificar se já existe uma marca com o mesmo nome para o usuário
        const existingQuery = await db.collection('brands')
          .where('userId', '==', userId)
          .where('name', '==', input.name)
          .get();
        
        if (!existingQuery.empty) {
          throw new Error('Já existe uma marca com este nome');
        }
        
        const brandData = {
          userId,
          name: input.name,
          description: input.description || '',
          logo: input.logo || '',
          website: input.website || '',
          industry: input.industry || '',
          size: input.size || '',
          status: input.status || 'active',
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        const docRef = await db.collection('brands').add(brandData);
        
        const result = {
          id: docRef.id,
          userId,
          name: brandData.name,
          description: brandData.description || '',
          logo: brandData.logo || '',
          website: brandData.website || '',
          industry: brandData.industry || '',
          size: brandData.size || '',
          status: brandData.status || 'active',
          createdAt: brandData.createdAt,
          updatedAt: brandData.updatedAt
        };
        
        // 🚀 Atualizar contador de estatísticas
        await statsCache.updateStatsCounter(userId, 'add', 'brand');
        
        return result;
        
      } catch (error) {
       
        throw new Error(error instanceof Error ? error.message : 'Erro ao criar marca');
      }
    },

    async updateBrand(parent: any, { id, input }: any, context: GraphQLContext) {
     
      
      try {
        const userId = context.user?.uid;
        
        if (!userId) {
          throw new Error('Usuário não autenticado');
        }
        
        // Verificar se a marca existe e pertence ao usuário
        const brandDoc = await db.collection('brands').doc(id).get();
        
        if (!brandDoc.exists) {
          throw new Error('Marca não encontrada');
        }
        
        const brandData = brandDoc.data();
        if (brandData?.userId !== userId) {
          throw new Error('Você não tem permissão para editar esta marca');
        }
        
        // Verificar duplicata de nome (excluindo a própria marca)
        if (input.name && input.name !== brandData.name) {
          const existingQuery = await db.collection('brands')
            .where('userId', '==', userId)
            .where('name', '==', input.name)
            .get();
          
          if (!existingQuery.empty) {
            throw new Error('Já existe uma marca com este nome');
          }
        }
        
        const updateData = {
          ...input,
          updatedAt: new Date()
        };
        
        await db.collection('brands').doc(id).update(updateData);
        
        // Buscar dados atualizados
        const updatedDoc = await db.collection('brands').doc(id).get();
        const updatedData = updatedDoc.data();
        
        const result = {
          id: updatedDoc.id,
          userId: updatedData?.userId || brandData.userId,
          name: updatedData?.name || brandData.name,
          description: updatedData?.description || '',
          logo: updatedData?.logo || '',
          website: updatedData?.website || '',
          industry: updatedData?.industry || '',
          size: updatedData?.size || '',
          status: updatedData?.status || 'active', // Garantir que status nunca seja null
          createdAt: updatedData?.createdAt?.toDate?.() || new Date(),
          updatedAt: updatedData?.updatedAt?.toDate?.() || new Date()
        };
        
       
        return result;
        
      } catch (error) {
       
        throw new Error(error instanceof Error ? error.message : 'Erro ao atualizar marca');
      }
    },

    async deleteBrand(parent: any, { id }: { id: string }, context: GraphQLContext) {
     
      
      try {
        const userId = context.user?.uid;
        
        if (!userId) {
          throw new Error('Usuário não autenticado');
        }
        
        // Verificar se a marca existe e pertence ao usuário
        const brandDoc = await db.collection('brands').doc(id).get();
        
        if (!brandDoc.exists) {
          throw new Error('Marca não encontrada');
        }
        
        const brandData = brandDoc.data();
        if (brandData?.userId !== userId) {
          throw new Error('Você não tem permissão para deletar esta marca');
        }
        
       
        
        // 🆕 DELETE EM CASCATA: Buscar e deletar todas as associações da marca
        const associationsQuery = await db.collection('brand_influencers')
          .where('brandId', '==', id)
          .get();
        
        if (!associationsQuery.empty) {
         
          
          // Deletar todas as associações em lote
          const deletePromises = associationsQuery.docs.map(async (doc) => {
            const associationData = doc.data();
           
            return doc.ref.delete();
          });
          
          await Promise.all(deletePromises);
         
        } else {
         
        }
        
        // 🚀 Atualizar contador de estatísticas ANTES de deletar
        await statsCache.updateStatsCounter(userId, 'remove', 'brand');

        // Deletar a marca
        await db.collection('brands').doc(id).delete();
        
       
        return true;
        
      } catch (error) {
       
        throw new Error(error instanceof Error ? error.message : 'Erro ao deletar marca');
      }
    },

    // ===== SCREENSHOT MUTATIONS =====
    async uploadScreenshot(parent: any, { input }: any, context: GraphQLContext) {
      const timestamp = Date.now();
      
     
     

      try {
        // Validação de entrada obrigatória
        if (!input) {
          throw new Error('Input não fornecido para salvar screenshot');
        }

        if (!input.influencerId || !input.platform) {
          throw new Error(`Dados obrigatórios em falta: influencerId(${!!input.influencerId}), platform(${!!input.platform})`);
        }

        if (!input.fileData && !input.url) {
          throw new Error('Deve fornecer fileData (base64) ou url já processada');
        }

       

        let finalUrl: string;

        // Processar upload se fileData fornecido
        if (input.fileData) {
         
          
          try {
            // Extrair dados base64 (remover o prefixo data:image/...)
            const base64Data = input.fileData.includes(',') 
              ? input.fileData.split(',')[1] 
              : input.fileData;
            
            const buffer = Buffer.from(base64Data, 'base64');
           

            // Simular objeto File para a função uploadScreenshot
            const mockFile = {
              arrayBuffer: async () => buffer.buffer,
              name: input.filename || `screenshot-${Date.now()}.png`,
              type: input.contentType || 'image/png',
              size: input.size || buffer.length
            } as File;

            // Importar dinamicamente a função de upload
            const { uploadScreenshot } = await import('./firebase-storage');
            finalUrl = await uploadScreenshot(mockFile, input.influencerId, input.platform);
            
            
          } catch (error) {
           
            throw new Error('Erro ao fazer upload do arquivo para o Storage');
          }
        } else {
          // Usar URL já fornecida
          finalUrl = input.url!;
          
        }

        // Operação no Firestore para salvar metadata
        const platformDocRef = db
          .collection('influencers')
          .doc(input.influencerId)
          .collection('screenshots')
          .doc(input.platform);

        

        // Buscar documento existente
        const platformDoc = await platformDocRef.get();
        
        let currentUrls: string[] = [];
        if (platformDoc.exists) {
          const data = platformDoc.data();
          currentUrls = data?.urls || [];
          
        } else {
          
        }

        const updatedUrls = [...currentUrls, finalUrl];

        // Atualizar Firestore com URL já processada
        await platformDocRef.set({
          platform: input.platform,
          urls: updatedUrls,
          lastUpdated: new Date(),
          totalScreenshots: updatedUrls.length
        }, { merge: true });

        

        // Criar resultado válido com todos os campos obrigatórios
        const result = {
          id: `${input.influencerId}_${input.platform}_${timestamp}`,
          influencerId: input.influencerId,
          platform: input.platform,
          url: finalUrl,
          filename: input.filename || `screenshot-${timestamp}.png`,
          size: input.size || 0,
          contentType: input.contentType || 'image/png',
          uploadedAt: new Date(),
          uploadedBy: context?.user?.uid || 'system'
        };

        
        return result;

      } catch (error) {
        
        throw new Error(`Erro ao salvar metadata: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      }
    },

    // Upload em lote de screenshots - OTIMIZADO
    async uploadScreenshotsBatch(parent: any, { input }: any, context: GraphQLContext) {
      

      try {
        // Validação de entrada obrigatória
        if (!input || !input.influencerId || !input.screenshots || input.screenshots.length === 0) {
          
          return {
            success: false,
            totalUploaded: 0,
            results: [],
            errors: [{
              platform: 'unknown',
              filename: 'unknown',
              error: 'Input inválido: influencerId e screenshots são obrigatórios'
            }]
          };
        }

        

        const results: any[] = [];
        const errors: any[] = [];
        let uploadedCount = 0;
        const timestamp = Date.now();

        // Processar uploads sequencialmente para melhor debug
        for (let i = 0; i < input.screenshots.length; i++) {
          const screenshot = input.screenshots[i];
          
          try {
           

            // Validação individual do screenshot
            if (!screenshot.platform || !screenshot.filename || !screenshot.fileData) {
              
              errors.push({
                platform: screenshot.platform || 'unknown',
                filename: screenshot.filename || 'unknown',
                error: 'Dados obrigatórios em falta: platform, filename, fileData'
              });
              continue;
            }

            // 🔥 FAZER UPLOAD REAL PARA FIREBASE STORAGE
           
            
            // Extrair dados base64
            const base64Data = screenshot.fileData.includes(',') 
              ? screenshot.fileData.split(',')[1] 
              : screenshot.fileData;
            
            const buffer = Buffer.from(base64Data, 'base64');
           

            // Simular objeto File para a função uploadScreenshot
            const mockFile = {
              arrayBuffer: async () => buffer.buffer,
              name: screenshot.filename,
              type: screenshot.contentType || 'image/png',
              size: screenshot.size || buffer.length
            } as File;

           
            
            // Fazer upload REAL para Firebase Storage
            const { uploadScreenshot } = await import('./firebase-storage');
            const realUrl = await uploadScreenshot(mockFile, input.influencerId, screenshot.platform);
            
           

            // Atualizar Firestore
            const { db } = await import('./firebase-admin');
            const { FieldValue } = await import('./firebase-admin');
            
            const platformDocRef = db
              .collection('influencers')
              .doc(input.influencerId)
              .collection('screenshots')
              .doc(screenshot.platform);

            const platformDoc = await platformDocRef.get();
            let currentUrls: string[] = [];
            
            if (platformDoc.exists) {
              const data = platformDoc.data();
              currentUrls = data?.urls || [];
            }

            const updatedUrls = [...currentUrls, realUrl];

            await platformDocRef.set({
              platform: screenshot.platform,
              urls: updatedUrls,
              lastUpdated: new Date(),
              totalScreenshots: updatedUrls.length
            }, { merge: true });

            // Criar resultado de sucesso
            const result = {
              id: `${input.influencerId}_${screenshot.platform}_${timestamp}_${i}`,
              influencerId: input.influencerId,
              platform: screenshot.platform,
              url: realUrl,
              filename: screenshot.filename,
              size: screenshot.size || 0,
              contentType: screenshot.contentType || 'image/png',
              uploadedAt: new Date(),
              uploadedBy: context?.user?.uid || 'system'
            };

            results.push(result);
            uploadedCount++;

           

          } catch (error) {
           
            errors.push({
              platform: screenshot.platform || 'unknown',
              filename: screenshot.filename || 'unknown',
              error: error instanceof Error ? error.message : 'Erro desconhecido'
            });
          }
        }

       

        return {
          success: uploadedCount > 0,
          totalUploaded: uploadedCount,
          results,
          errors
        };

      } catch (error) {
       
        
        return {
          success: false,
          totalUploaded: 0,
          results: [],
          errors: [{
            platform: 'unknown',
            filename: 'unknown',
            error: error instanceof Error ? error.message : 'Erro desconhecido'
          }]
        };
      }
    },

    // ===== AVATAR MUTATIONS =====
    async uploadAvatar(parent: any, { input }: any, context: GraphQLContext) {
     
     

      try {
        // Validação de entrada obrigatória
        if (!input) {
          throw new Error('Input não fornecido para upload de avatar');
        }

        if (!input.userId || !input.fileData) {
          throw new Error(`Dados obrigatórios em falta: userId(${!!input.userId}), fileData(${!!input.fileData})`);
        }

       

        // Processar dados do arquivo base64
        let buffer: Buffer;
        try {
          // Extrair dados base64 (remover o prefixo data:image/...)
          const base64Data = input.fileData.includes(',') 
            ? input.fileData.split(',')[1] 
            : input.fileData;
          
          buffer = Buffer.from(base64Data, 'base64');
         
        } catch (error) {
          throw new Error('Erro ao processar dados do arquivo');
        }

        // Fazer upload para Firebase Storage
        let avatarUrl: string;
        try {
          // Simular objeto File para a função uploadUserAvatar
          const mockFile = {
            arrayBuffer: async () => buffer.buffer,
            name: input.filename || `avatar-${Date.now()}.jpg`,
            type: input.contentType || 'image/jpeg',
            size: input.size || buffer.length
          } as File;

          // Importar dinamicamente a função de upload
          const { uploadUserAvatar } = await import('./firebase-storage');
          avatarUrl = await uploadUserAvatar(mockFile, input.userId);
          
         
        } catch (error) {
         
          throw new Error('Erro ao fazer upload do arquivo para o Storage');
        }

        // Atualizar documento do usuário no Firestore
        try {
          const { updateUserAvatar } = await import('./firebase-admin');
          await updateUserAvatar(input.userId, avatarUrl);
         
        } catch (error) {
         
          throw new Error('Erro ao salvar avatar no banco de dados');
        }

        // Criar resultado válido
        const result = {
          id: `avatar_${input.userId}_${Date.now()}`,
          userId: input.userId,
          avatarUrl,
          filename: input.filename || `avatar-${Date.now()}.jpg`,
          size: input.size || buffer.length,
          contentType: input.contentType || 'image/jpeg',
          uploadedAt: new Date(),
          success: true,
          message: 'Avatar atualizado com sucesso!'
        };

       
        return result;

      } catch (error) {
       
        throw new Error(`Erro no upload de avatar: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      }
    },

    async deleteScreenshot(parent: any, { id, influencerId, platform }: { id: string, influencerId: string, platform: string }, context: GraphQLContext) {
     
      
      try {
        // Referenciar o documento da plataforma na subcoleção screenshots
        const platformDocRef = db
          .collection('influencers')
          .doc(influencerId)
          .collection('screenshots')
          .doc(platform);

        // Buscar documento da plataforma
        const platformDoc = await platformDocRef.get();
        
        if (!platformDoc.exists) {
         
          return false;
        }

        const data = platformDoc.data();
        const currentUrls = data?.urls || [];

        // Buscar a URL no array (o ID é a própria URL do screenshot)
        const urlToDelete = id;
        const urlIndex = currentUrls.findIndex((url: string) => url === urlToDelete);

        if (urlIndex === -1) {
         
          return false;
        }

       

        // 🔥 DELETAR ARQUIVO DO FIREBASE STORAGE PRIMEIRO
        try {
          const { deleteScreenshot } = await import('./firebase-storage');
          const deleted = await deleteScreenshot(urlToDelete);
          if (deleted) {
           
          } else {
           
          }
        } catch (storageError) {
          
          // Continuar mesmo se Storage falhar - remover do Firestore
        }

        // Remover URL do array
        const updatedUrls = currentUrls.filter((url: string) => url !== urlToDelete);

        // Atualizar documento da plataforma
        if (updatedUrls.length === 0) {
          // Se não há mais URLs, deletar o documento inteiro
          await platformDocRef.delete();
          
        } else {
          // Atualizar com as URLs restantes
          await platformDocRef.update({
            urls: updatedUrls,
            lastUpdated: new Date(),
            totalScreenshots: updatedUrls.length
          });
          
        }
        
        return true;
      } catch (error) {
        
        // Retornar false em caso de erro ao invés de throw
        return false;
      }
    },

    // ===== CATEGORY MUTATIONS =====
    async createCategory(parent: any, { input }: any, context: GraphQLContext) {
      try {
        const userId = context.user?.uid;
        
        if (!userId) {
          throw new Error('Usuário não autenticado');
        }

        if (!input.name) {
          throw new Error('Nome da categoria é obrigatório');
        }

        const categoryData = {
          name: input.name,
          userId: input.userId || userId,
          isActive: input.isActive !== false,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const docRef = await db.collection('categories').add(categoryData);
        const newCategory = { id: docRef.id, ...categoryData };

        return newCategory;
      } catch (error) {
        throw new Error(error instanceof Error ? error.message : 'Erro ao criar categoria');
      }
    },

    async updateCategory(parent: any, { id, input }: any, context: GraphQLContext) {
      try {
        const userId = context.user?.uid;
        
        if (!userId) {
          throw new Error('Usuário não autenticado');
        }

        const categoryDoc = await db.collection('categories').doc(id).get();
        
        if (!categoryDoc.exists) {
          throw new Error('Categoria não encontrada');
        }

        const categoryData = categoryDoc.data();
        if (categoryData?.userId !== userId) {
          throw new Error('Você não tem permissão para editar esta categoria');
        }

        const updateData = {
          ...(input.name && { name: input.name }),
          ...(input.isActive !== undefined && { isActive: input.isActive }),
          updatedAt: new Date()
        };

        await db.collection('categories').doc(id).update(updateData);

        const updatedCategory = {
          id,
          ...categoryData,
          ...updateData
        };

        return updatedCategory;
      } catch (error) {
        throw new Error(error instanceof Error ? error.message : 'Erro ao atualizar categoria');
      }
    },

    async deleteCategory(parent: any, { id, userId }: { id: string, userId: string }, context: GraphQLContext) {
      try {
        const authUserId = context.user?.uid;
        
        if (!authUserId) {
          throw new Error('Usuário não autenticado');
        }

        if (authUserId !== userId) {
          throw new Error('Você não tem permissão para deletar esta categoria');
        }

        const categoryDoc = await db.collection('categories').doc(id).get();
        
        if (!categoryDoc.exists) {
          throw new Error('Categoria não encontrada');
        }

        const categoryData = categoryDoc.data();
        if (categoryData?.userId !== userId) {
          throw new Error('Você não tem permissão para deletar esta categoria');
        }

        await db.collection('categories').doc(id).delete();
        
        return true;
      } catch (error) {
        throw new Error(error instanceof Error ? error.message : 'Erro ao deletar categoria');
      }
    },

    // ===== NOTE MUTATIONS =====
    async createNote(parent: any, { input }: any, context: GraphQLContext) {
      try {
        const userId = context.user?.uid;
        
        if (!userId) {
          throw new Error('Usuário não autenticado');
        }

        if (!input.title || !input.influencerId) {
          throw new Error('Título e ID do influenciador são obrigatórios');
        }

        const noteData = {
          title: input.title,
          content: input.content || '',
          type: input.type || 'general',
          influencerId: input.influencerId,
          userId: input.userId || userId,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const docRef = await db.collection('notes').add(noteData);
        const newNote = { id: docRef.id, ...noteData };

        return newNote;
      } catch (error) {
        throw new Error(error instanceof Error ? error.message : 'Erro ao criar nota');
      }
    },

    async updateNote(parent: any, { id, input }: any, context: GraphQLContext) {
      try {
        const userId = context.user?.uid;
        
        if (!userId) {
          throw new Error('Usuário não autenticado');
        }

        const noteDoc = await db.collection('notes').doc(id).get();
        
        if (!noteDoc.exists) {
          throw new Error('Nota não encontrada');
        }

        const noteData = noteDoc.data();
        if (noteData?.userId !== userId) {
          throw new Error('Você não tem permissão para editar esta nota');
        }

        const updateData = {
          ...(input.title && { title: input.title }),
          ...(input.content !== undefined && { content: input.content }),
          ...(input.type && { type: input.type }),
          updatedAt: new Date()
        };

        await db.collection('notes').doc(id).update(updateData);

        const updatedNote = {
          id,
          ...noteData,
          ...updateData
        };

        return updatedNote;
      } catch (error) {
        throw new Error(error instanceof Error ? error.message : 'Erro ao atualizar nota');
      }
    },

    async deleteNote(parent: any, { id, userId }: { id: string, userId: string }, context: GraphQLContext) {
      try {
        const authUserId = context.user?.uid;
        
        if (!authUserId) {
          throw new Error('Usuário não autenticado');
        }

        if (authUserId !== userId) {
          throw new Error('Você não tem permissão para deletar esta nota');
        }

        const noteDoc = await db.collection('notes').doc(id).get();
        
        if (!noteDoc.exists) {
          throw new Error('Nota não encontrada');
        }

        const noteData = noteDoc.data();
        if (noteData?.userId !== userId) {
          throw new Error('Você não tem permissão para deletar esta nota');
        }

        await db.collection('notes').doc(id).delete();
        
        return true;
      } catch (error) {
        throw new Error(error instanceof Error ? error.message : 'Erro ao deletar nota');
      }
    },

    // ===== 🆕 MUTATIONS DE ORÇAMENTOS COM CONTROLE DE ACESSO =====

    // 💼 CRIAR CONTRAPROPOSTA DE COLABORADOR
    createCollaboratorCounterProposal: async (parent: any, { input }: any, context: GraphQLContext) => {
      
      
      try {
        const authUserId = context.user?.uid;
        
        if (!authUserId) {
          throw new Error('Usuário não autenticado');
        }

        // Validar dados obrigatórios
        if (!input.budgetId || !input.proposalId || !input.influencerId || !input.originalAmount || !input.proposedAmount) {
          throw new Error('Dados obrigatórios: budgetId, proposalId, influencerId, originalAmount, proposedAmount');
        }

        // Verificar se o usuário autenticado é o mesmo do input
        if (authUserId !== input.proposedBy.userId) {
          throw new Error('Você só pode criar contrapropostas em seu próprio nome');
        }

        const { BudgetService } = await import('@/services/budget-service');
        
        const counterProposal = await BudgetService.createCollaboratorCounterProposal({
          budgetId: input.budgetId,
          proposalId: input.proposalId,
          influencerId: input.influencerId,
          originalAmount: input.originalAmount,
          proposedAmount: input.proposedAmount,
          currency: input.currency || 'BRL',
          notes: input.notes,
          serviceType: input.serviceType,
          proposedBy: {
            userId: authUserId,
            userName: input.proposedBy.userName,
            userEmail: input.proposedBy.userEmail,
            collaboratorRole: input.proposedBy.collaboratorRole
          }
        });

        
        return counterProposal;

      } catch (error) {
        
        throw new Error(error instanceof Error ? error.message : 'Erro ao criar contraproposta');
      }
    },

    // ✏️ ATUALIZAR CONTRAPROPOSTA DE COLABORADOR
    updateCollaboratorCounterProposal: async (parent: any, { id, input }: any, context: GraphQLContext) => {

      
      try {
        const authUserId = context.user?.uid;
        
        if (!authUserId) {
          throw new Error('Usuário não autenticado');
        }

        // Buscar a contraproposta existente para validar ownership
        const counterProposalQuery = await db.collectionGroup('collaboratorCounterProposals')
          .where('proposedBy.userId', '==', authUserId)
          .get();

        let counterProposalDoc = null;
        counterProposalQuery.docs.forEach(doc => {
          if (doc.id === id) {
            counterProposalDoc = doc;
          }
        });

        if (!counterProposalDoc) {
          throw new Error('Contraproposta não encontrada ou você não tem permissão para editá-la');
        }

        const updateData = {
          ...(input.proposedAmount !== undefined && { proposedAmount: input.proposedAmount }),
          ...(input.notes !== undefined && { notes: input.notes }),
          ...(input.currency && { currency: input.currency }),
          updatedAt: new Date()
        };

        await counterProposalDoc.ref.update(updateData);

        const updatedCounterProposal = {
          id: counterProposalDoc.id,
          ...counterProposalDoc.data(),
          ...updateData
        };

        
        return updatedCounterProposal;

      } catch (error) {
        
        throw new Error(error instanceof Error ? error.message : 'Erro ao atualizar contraproposta');
      }
    },

    // 👨‍💼 REVISAR CONTRAPROPOSTA DE COLABORADOR (apenas proprietários)
    reviewCollaboratorCounterProposal: async (parent: any, { id, action, reviewNote, reviewedBy }: any, context: GraphQLContext) => {
     
      
      try {
        const authUserId = context.user?.uid;
        
        if (!authUserId) {
          throw new Error('Usuário não autenticado');
        }

        if (authUserId !== reviewedBy) {
          throw new Error('Você só pode revisar contrapropostas em seu próprio nome');
        }

        // Buscar a contraproposta
        const counterProposalQuery = await db.collectionGroup('collaboratorCounterProposals').get();
        let counterProposalDoc = null;
        let proposalId = '';

        counterProposalQuery.docs.forEach(doc => {
          if (doc.id === id) {
            counterProposalDoc = doc;
            proposalId = doc.data().proposalId;
          }
        });

        if (!counterProposalDoc) {
          throw new Error('Contraproposta não encontrada');
        }

        // Verificar se o usuário é proprietário da proposta
        const { BudgetService } = await import('@/services/budget-service');
        const accessResult = await BudgetService.canUserAccessBudgets(proposalId, authUserId);
        
        if (!accessResult.hasAccess || accessResult.role !== 'owner') {
          throw new Error('Apenas o proprietário da proposta pode revisar contrapropostas');
        }

        const updateData = {
          status: action === 'accept' ? 'accepted' : 'rejected',
          reviewedAt: new Date(),
          reviewedBy: authUserId,
          reviewNote: reviewNote || '',
          updatedAt: new Date()
        };

        await counterProposalDoc.ref.update(updateData);

        const reviewedCounterProposal = {
          id: counterProposalDoc.id,
          ...counterProposalDoc.data(),
          ...updateData
        };

        
        return reviewedCounterProposal;

      } catch (error) {
        
        throw new Error(error instanceof Error ? error.message : 'Erro ao revisar contraproposta');
      }
    },

    // 🗑️ DELETAR CONTRAPROPOSTA DE COLABORADOR
    deleteCollaboratorCounterProposal: async (parent: any, { id }: { id: string }, context: GraphQLContext) => {
     
      
      try {
        const authUserId = context.user?.uid;
        
        if (!authUserId) {
          throw new Error('Usuário não autenticado');
        }

        // Buscar a contraproposta para validar ownership
        const counterProposalQuery = await db.collectionGroup('collaboratorCounterProposals')
          .where('proposedBy.userId', '==', authUserId)
          .get();

        let counterProposalDoc = null;
        counterProposalQuery.docs.forEach(doc => {
          if (doc.id === id) {
            counterProposalDoc = doc;
          }
        });

        if (!counterProposalDoc) {
          throw new Error('Contraproposta não encontrada ou você não tem permissão para deletá-la');
        }

        // Só permitir deleção se estiver pendente
        const counterProposalData = counterProposalDoc.data();
        if (counterProposalData.status !== 'pending') {
          throw new Error('Só é possível deletar contrapropostas pendentes');
        }

        await counterProposalDoc.ref.delete();

        
        return true;

      } catch (error) {
        
        throw new Error(error instanceof Error ? error.message : 'Erro ao deletar contraproposta');
      }
    }
  }
};

// Função para criar context
export async function createGraphQLContext(req: any): Promise<GraphQLContext> {
 
  return {
    user: await getUserFromRequest(req)
  };
}

// Função para criar context sem autenticação (fallback)
export function createGraphQLContextNoAuth(req: any): GraphQLContext {
 
  return {
    user: null
  };
}

// Extração de usuário do request
async function getUserFromRequest(req: any): Promise<{ uid: string; email?: string } | null> {
  try {
    
    
    const authHeader = req.headers?.authorization || req.headers?.Authorization;
    
    if (!authHeader) {
      
      return null;
    }
    
    let token: string;
    if (authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    } else {
      token = authHeader;
    }
    
    if (!token) {

      return null;
    }
    
    
    
    // 🔐 MELHORADO: Extração mais robusta do userId do token JWT do Clerk
    try {
      // Verificar se o token tem formato JWT válido (3 partes separadas por ponto)
      const tokenParts = token.split('.');
      if (tokenParts.length !== 3) {
        
        return null;
      }
      
      // Extrair payload do token JWT (sem verificação de assinatura)
      const base64Url = tokenParts[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      
      // Adicionar padding se necessário
      const padding = base64.length % 4;
      const paddedBase64 = padding ? base64 + '='.repeat(4 - padding) : base64;
      
      const jsonPayload = Buffer.from(paddedBase64, 'base64').toString('utf8');
      const payload = JSON.parse(jsonPayload);
      
      
      
      // Verificar se o token está expirado
      if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
        
        return null;
      }
      
      if (payload.sub) {
        
        return {
          uid: payload.sub,
          email: payload.email || undefined
        };
      }
      
      
      return null;
      
    } catch (tokenError) {
      
      
      // Se falhou a decodificação JWT, tentar usar o token diretamente como userId (fallback)
      if (token.startsWith('user_')) {
        
        return {
          uid: token,
          email: undefined
        };
      }
      
      return null;
    }
  } catch (error) {

    return null;
  }
}

export default resolvers; 

