/**
 * 🔓 HOOK PARA CATEGORIAS PÚBLICAS
 * Hook especializado para páginas públicas/compartilhadas que garante
 * que sempre haja categorias disponíveis, mesmo sem autenticação
 */

import { useMemo } from 'react';
import { useCategories } from './use-graphql-influencers';

interface PublicCategory {
  id: string;
  name: string;
  slug: string;
  isActive: boolean;
  description?: string;
}

interface UsePublicCategoriesOptions {
  userId?: string | null;
  includeBasicCategories?: boolean;
}

/**
 * Hook para buscar categorias em contextos públicos
 * Garante que sempre haja categorias disponíveis, mesmo sem autenticação
 */
export function usePublicCategories(options: UsePublicCategoriesOptions = {}) {
  const { userId, includeBasicCategories = true } = options;
  
  // Usar o hook principal de categorias
  const { categories: rawCategories, loading, error, refetch } = useCategories(userId);
  
  // Categorias básicas para fallback
  const basicCategories: PublicCategory[] = useMemo(() => [
    { id: 'all', name: '<PERSON><PERSON>', slug: 'all', isActive: true, description: 'Todas as categorias' },
    { id: 'lifestyle', name: 'Lifestyle', slug: 'lifestyle', isActive: true, description: 'Conteúdo de lifestyle' },
    { id: 'fashion', name: 'Moda', slug: 'fashion', isActive: true, description: 'Conteúdo de moda' },
    { id: 'beauty', name: 'Beleza', slug: 'beauty', isActive: true, description: 'Conteúdo de beleza' },
    { id: 'fitness', name: 'Fitness', slug: 'fitness', isActive: true, description: 'Conteúdo de fitness' },
    { id: 'food', name: 'Gastronomia', slug: 'food', isActive: true, description: 'Conteúdo gastronômico' },
    { id: 'travel', name: 'Viagem', slug: 'travel', isActive: true, description: 'Conteúdo de viagem' },
    { id: 'tech', name: 'Tecnologia', slug: 'tech', isActive: true, description: 'Conteúdo de tecnologia' }
  ], []);
  
  // Processar categorias com fallback garantido
  const categories = useMemo(() => {
    // Se há categorias carregadas, usar elas
    if (rawCategories && rawCategories.length > 0) {
      return rawCategories;
    }
    
    // Se está carregando, retornar array vazio (será mostrado loading)
    if (loading) {
      return [];
    }
    
    // Se há erro ou não há categorias e includeBasicCategories é true, usar categorias básicas
    if ((error || rawCategories.length === 0) && includeBasicCategories) {
      console.log('🔓 [usePublicCategories] Usando categorias básicas como fallback');
      return basicCategories;
    }
    
    // Caso contrário, retornar as categorias carregadas (mesmo que vazio)
    return rawCategories;
  }, [rawCategories, loading, error, includeBasicCategories, basicCategories]);
  
  // Estado de loading: true apenas se está realmente carregando e não há categorias
  const isLoading = loading && categories.length === 0;
  
  // Estado de erro: apenas se há erro e não conseguiu carregar categorias básicas
  const hasError = error && !includeBasicCategories;
  
  return {
    categories,
    loading: isLoading,
    error: hasError ? error : null,
    refetch,
    // Informações adicionais
    isUsingFallback: categories === basicCategories,
    hasBasicCategories: includeBasicCategories
  };
}

/**
 * Hook simplificado para páginas públicas que sempre retorna categorias
 * Ideal para páginas compartilhadas onde é essencial ter categorias disponíveis
 */
export function usePublicCategoriesAlways(userId?: string | null) {
  return usePublicCategories({ 
    userId, 
    includeBasicCategories: true 
  });
}

/**
 * Hook para páginas autenticadas que pode falhar se não houver categorias
 * Ideal para páginas privadas onde é aceitável não ter categorias
 */
export function usePublicCategoriesOptional(userId?: string | null) {
  return usePublicCategories({ 
    userId, 
    includeBasicCategories: false 
  });
}
